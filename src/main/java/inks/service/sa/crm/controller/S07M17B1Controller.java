package inks.service.sa.crm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.*;
import inks.common.core.enums.BusinessType;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.annotation.OperLog;
import inks.sa.common.core.annotation.PreAuthorize;
import inks.sa.common.core.config.oa.OAController;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.service.SaJustauthService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.utils.OAUtil;
import inks.sa.common.core.utils.PrintColor;
import inks.service.sa.crm.domain.pojo.SaSmpplanPojo;
import inks.service.sa.crm.domain.pojo.SaSmpplanitemPojo;
import inks.service.sa.crm.service.SaSmpplanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.StringWriter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 样品需求(Sa_SmpPlan)表控制层
 *
 * <AUTHOR>
 * @since 2024-09-03 15:17:53
 */
@RestController
@RequestMapping("S07M17B1")
@Api(tags = "S07M17B1:样品需求")
public class S07M17B1Controller extends SaSmpplanController {
    @Resource
    private SaSmpplanService saSmpplanService;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaJustauthService saJustauthService;
    @Resource
    private OAController OAController;


    @ApiOperation(value = "中止批量List<SaSmpplanitemPojo>", notes = "中止销售订单,?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.POST)
    @PreAuthorize(hasPermi = "Bus_Machining.Edit")
    @OperLog(title = "中止销售订单", businessType = BusinessType.UPDATE)
    public R<SaSmpplanPojo> closed(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<SaSmpplanitemPojo> lst = JSONArray.parseArray(json, SaSmpplanitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSmpplanService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
    public R<ApprrecPojo> sendapprovel(String key, String apprid, String type, String remark) {
        try {
            if (type == null) type = "wxe";  // 默认走企业微信
            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
            //获取token
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //从redis中获取模板对象
            // Object obj = saRedisService.getCacheObject(verifyKey);
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            ApprovePojo approvePojo = new ApprovePojo();
            SaSmpplanPojo billEntity = this.saSmpplanService.getBillEntity(key);
            Map<String, Object> billMap = OAUtil.billToOAMap(billEntity);
            approvePojo.setObject(billMap);

            // 发起oms审批,先判断是否正在审批 (最下面发起oms审批成功后需设置OaFlowMark=1)
            if (billEntity.getOaflowmark() != null && billEntity.getOaflowmark() == 1) {
                return R.fail("该单据已发起OA审批");
            }
            if ("oms".equals(type)) {
//                // 发起oms审批,先判断是否正在审批 (最下面发起oms审批成功后需设置OaFlowMark=1)
//                if (machiningBillEntity.getOaflowmark() != null && machiningBillEntity.getOaflowmark() == 1) {
//                    return R.fail("该单据已发起OA审批");
//                }
                //创建VM数据对象
                VelocityContext context = new VelocityContext();
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();
                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();
                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法
                //写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
//                String data = JSONObject.toJSONString(approvePojo.getObject());
//                apprrecPojo.setDatatemp(data);
            } else {
                //创建VM数据对象
                VelocityContext context = new VelocityContext();

                //获得第三方账号
                SaJustauthPojo justAuth = saJustauthService.getJustauthByUserid(loginUser.getUserid(), type, null);
                JustauthPojo justauthPojo = new JustauthPojo();
                if (justAuth == null) {
                    PrintColor.red("审批发起人 获得第三方账号出错");
                    return R.fail("审批发起人 获得第三方账号出错");
                }
                org.springframework.beans.BeanUtils.copyProperties(justAuth, justauthPojo);

                approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
                approvePojo.setUserid(justauthPojo.getAuthuuid());
                approvePojo.setModelcode(apprrecPojo.getTemplateid());
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();
                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();
                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法
                //写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
            }


            //新建审批记录
            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            apprrecPojo.setApprname("订单审批");
            apprrecPojo.setResultcode("");
            apprrecPojo.setBillid(key);    // 单据ID
            apprrecPojo.setUserid("");
            apprrecPojo.setApprtype("");
            apprrecPojo.setCreateby(loginUser.getRealname());
            apprrecPojo.setCreatebyid(loginUser.getUserid());
            apprrecPojo.setCreatedate(new Date());
            apprrecPojo.setLister(loginUser.getRealname());
            apprrecPojo.setListerid(loginUser.getUserid());
            apprrecPojo.setModifydate(new Date());
            apprrecPojo.setTenantid(loginUser.getTenantid());
            //发起Flowable审批时加入的评论备注
            apprrecPojo.setRemark(remark == null ? "" : remark);
            //将企业微信审批信息存入redis
            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            saRedisService.setKeyValue(CachKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);
            if ("wxe".equals(type) || "ding".equals(type)) {
                R r = this.OAController.sendApproval(apprrecPojo.getId(), type);
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r.getMsg());
                }
            }

//            else {//type为oms
//                R r = this.aDingAndWxeController.omsapprovel(apprrecPojo.getId(), loginUser.getTenantid(), loginUser.getToken());
//                if (r.getCode() != 200) {
//                    return R.fail("发起审批失败" + r.getMsg());
//                }
//                // 发起oms审批成功,需设置OaFlowMark=1 并更新单据
//                machiningBillEntity.setOaflowmark(1);
//                busMachiningService.update(machiningBillEntity);
//            }
            // 发起oms审批成功,需设置OaFlowMark=1 并更新单据
            billEntity.setOaflowmark(1);
            saSmpplanService.updateOaflowmark(billEntity);
            return R.ok(apprrecPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
    public R<SaSmpplanPojo> justapprovel(String key, String type, String approved) {
        try {
            PrintColor.red("/justapprovel 审批回调修改状态 approved:" + approved);
            //1.读取审批记录
            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            PrintColor.red("商机回调apprrecPojo" + JSON.toJSONString(apprrecPojo));
            //2.1 获得单据数据
            SaSmpplanPojo billPojo = this.saSmpplanService.getEntity(apprrecPojo.getBillid());
            //3. 写入审核批
            //获得第三方账号
            if (type == null) type = "wxe";
            // oms审批即将完成,需设置OaFlowMark=0
            billPojo.setOaflowmark(0);
            saSmpplanService.updateOaflowmark(billPojo);//只更新oaflowmark
            // 若oms审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
            if ("false".equals(approved)) {
                return R.ok();
            }
            if ("oms".equals(type)) {
//                // oms审批即将完成,需设置OaFlowMark=0
//                busMachiningPojo.setOaflowmark(0);
//                busMachiningMapper.updateOaflowmark(busMachiningPojo);//只更新oaflowmark
//                // 若oms审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
//                if ("false".equals(approved)) {
//                    return R.ok();
//                }
                // 点击同意审批：审批人字段赋值, if包裹外面的approval方法会进行审核
                billPojo.setAssessorid(apprrecPojo.getUserid());
                billPojo.setAssessor(apprrecPojo.getRealname()); //审核员
            } else {
                SaJustauthPojo justauthByUuid = saJustauthService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type, null);
                if (justauthByUuid == null) {
                    PrintColor.red("D03M01B1/justapprovel审批回调失败,未找到第三方账号");
                    return R.fail("审批回调失败,未找到第三方账号");
                }
                JustauthPojo justauthPojo = new JustauthPojo();
                org.springframework.beans.BeanUtils.copyProperties(justauthByUuid, justauthPojo);
                billPojo.setAssessorid(justauthPojo.getUserid());
                billPojo.setAssessor(justauthPojo.getRealname()); //审核员
            }
            billPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saSmpplanService.approval(billPojo));
        } catch (Exception e) {
            System.out.println("写入审核失败：" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }
}
