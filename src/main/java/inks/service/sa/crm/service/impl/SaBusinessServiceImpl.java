package inks.service.sa.crm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.*;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.sa.common.core.config.oa.OAController;
import inks.sa.common.core.domain.pojo.SaJustauthPojo;
import inks.sa.common.core.service.SaJustauthService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.crm.config.ApprovalRequestDto;
import inks.service.sa.crm.domain.SaBusinessEntity;
import inks.service.sa.crm.domain.SaBusinessitemEntity;
import inks.service.sa.crm.domain.SaBusinessstageEntity;
import inks.service.sa.crm.domain.pojo.*;
import inks.service.sa.crm.mapper.SaBusinessMapper;
import inks.service.sa.crm.mapper.SaBusinessitemMapper;
import inks.service.sa.crm.mapper.SaBusinessstageMapper;
import inks.service.sa.crm.mapper.SaCustomerMapper;
import inks.service.sa.crm.service.SaBusinessService;
import inks.service.sa.crm.service.SaBusinessitemService;
import inks.service.sa.crm.service.SaBusinessstageService;
import inks.service.sa.crm.service.SaFollowviewService;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.StringWriter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 商机(SaBusiness)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-20 14:21:30
 */
@Service("saBusinessService")
public class SaBusinessServiceImpl implements SaBusinessService {
    @Resource
    private SaCustomerMapper saCustomerMapper;

    @Resource
    private SaBusinessMapper saBusinessMapper;

    @Resource
    private SaBusinessitemMapper saBusinessitemMapper;
    @Resource
    private SaBusinessstageMapper saBusinessstageMapper;
    @Resource
    private SaBusinessstageService saBusinessstageService;
    /**
     * 服务对象Item
     */
    @Resource
    private SaBusinessitemService saBusinessitemService;
    @Autowired
    private SaFollowviewService saFollowviewService;


    @Override
    public SaBusinessPojo getEntity(String key) {
        return this.saBusinessMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaBusinessitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBusinessitemdetailPojo> lst = saBusinessMapper.getPageList(queryParam);
            PageInfo<SaBusinessitemdetailPojo> pageInfo = new PageInfo<SaBusinessitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaBusinessPojo getBillEntity(String key) {
        try {
            //读取主表
            SaBusinessPojo saBusinessPojo = this.saBusinessMapper.getEntity(key);
            //读取子表
            saBusinessPojo.setItem(saBusinessitemMapper.getList(saBusinessPojo.getId()));
            //读取Stage子表
            saBusinessPojo.setStage(saBusinessstageMapper.getList(saBusinessPojo.getId()));
            return saBusinessPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaBusinessPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBusinessPojo> lst = saBusinessMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表、Stage子表
            for (SaBusinessPojo saBusinessPojo : lst) {
                saBusinessPojo.setItem(saBusinessitemMapper.getList(saBusinessPojo.getId()));
                saBusinessPojo.setStage(saBusinessstageMapper.getList(saBusinessPojo.getId()));
            }
            PageInfo<SaBusinessPojo> pageInfo = new PageInfo<SaBusinessPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaBusinessPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBusinessPojo> lst = saBusinessMapper.getPageTh(queryParam);
            PageInfo<SaBusinessPojo> pageInfo = new PageInfo<SaBusinessPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saBusinessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaBusinessPojo insert(SaBusinessPojo saBusinessPojo, LoginUser loginUser) {
//初始化NULL字段
        cleanNull(saBusinessPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaBusinessEntity saBusinessEntity = new SaBusinessEntity();
        BeanUtils.copyProperties(saBusinessPojo, saBusinessEntity);
        //设置id和新建日期
        saBusinessEntity.setId(id);
        saBusinessEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saBusinessMapper.insert(saBusinessEntity);
        //Item子表处理
        List<SaBusinessitemPojo> lst = saBusinessPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (SaBusinessitemPojo saBusinessitemPojo : lst) {
                //初始化item的NULL
                SaBusinessitemPojo itemPojo = this.saBusinessitemService.clearNull(saBusinessitemPojo);
                SaBusinessitemEntity saBusinessitemEntity = new SaBusinessitemEntity();
                BeanUtils.copyProperties(itemPojo, saBusinessitemEntity);
                //设置id和Pid
                saBusinessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saBusinessitemEntity.setPid(id);
                saBusinessitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saBusinessitemMapper.insert(saBusinessitemEntity);
            }
        }

        //Stage子表处理
        List<SaBusinessstagePojo> stageLst = saBusinessPojo.getStage();
        if (stageLst != null) {
            //循环每个Stage子表
            for (SaBusinessstagePojo saBusinessstagePojo : stageLst) {
                //初始化Stage的NULL
                SaBusinessstagePojo itemPojo = saBusinessstageService.clearNull(saBusinessstagePojo);
                SaBusinessstageEntity saBusinessstageEntity = new SaBusinessstageEntity();
                BeanUtils.copyProperties(itemPojo, saBusinessstageEntity);
                //设置id和Pid
                saBusinessstageEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saBusinessstageEntity.setPid(id);
                saBusinessstageEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saBusinessstageMapper.insert(saBusinessstageEntity);
            }
        }
        SaBusinessPojo billPojo = this.getBillEntity(saBusinessEntity.getId());
        // 创建客户跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "创建了商机【" + billPojo.getRefno() + billPojo.getBilltitle() + "】";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(billPojo.getGroupid(),
                "Customer",
                billPojo.getCustname(),
                "Business.insert",
                itemContent,
                loginUser);
        saFollowviewService.insert(buildFollowview);
        //返回Bill实例
        return billPojo;

    }

    /**
     * 修改数据
     *
     * @param saBusinessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaBusinessPojo update(SaBusinessPojo saBusinessPojo, LoginUser loginUser) {
        //主表更改
        SaBusinessEntity saBusinessEntity = new SaBusinessEntity();
        BeanUtils.copyProperties(saBusinessPojo, saBusinessEntity);
        this.saBusinessMapper.update(saBusinessEntity);
        if (saBusinessPojo.getItem() != null) {
            //Item子表处理
            List<SaBusinessitemPojo> lst = saBusinessPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saBusinessMapper.getDelItemIds(saBusinessPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.saBusinessitemMapper.delete(lstDelId);
                }
            }
            //循环每个item子表
            for (SaBusinessitemPojo saBusinessitemPojo : lst) {
                SaBusinessitemEntity saBusinessitemEntity = new SaBusinessitemEntity();
                if ("".equals(saBusinessitemPojo.getId()) || saBusinessitemPojo.getId() == null) {
                    //初始化item的NULL
                    SaBusinessitemPojo itemPojo = this.saBusinessitemService.clearNull(saBusinessitemPojo);
                    BeanUtils.copyProperties(itemPojo, saBusinessitemEntity);
                    //设置id和Pid
                    saBusinessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    saBusinessitemEntity.setPid(saBusinessEntity.getId());  // 主表 id
                    saBusinessitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.saBusinessitemMapper.insert(saBusinessitemEntity);
                } else {
                    BeanUtils.copyProperties(saBusinessitemPojo, saBusinessitemEntity);
                    this.saBusinessitemMapper.update(saBusinessitemEntity);
                }
            }
        }


        //Stage子表处理
        if (saBusinessPojo.getStage() != null) {
            List<SaBusinessstagePojo> stageLst = saBusinessPojo.getStage();
            //获取被删除的Stage
            List<String> lstDelIds = saBusinessMapper.getDelStageIds(saBusinessPojo);
            if (lstDelIds != null) {
                //循环每个删除Stage子表
                for (String lstDelId : lstDelIds) {
                    this.saBusinessstageMapper.delete(lstDelId);
                }
            }
            //循环每个Stage子表
            for (SaBusinessstagePojo saBusinessstagePojo : stageLst) {
                SaBusinessstageEntity saBusinessstageEntity = new SaBusinessstageEntity();
                if ("".equals(saBusinessstagePojo.getId()) || saBusinessstagePojo.getId() == null) {
                    //初始化Stage的NULL
                    SaBusinessstagePojo itemPojo = saBusinessstageService.clearNull(saBusinessstagePojo);
                    BeanUtils.copyProperties(itemPojo, saBusinessstageEntity);
                    //设置id和Pid
                    saBusinessstageEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // stage id
                    saBusinessstageEntity.setPid(saBusinessEntity.getId());  // 主表 id
                    saBusinessstageEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.saBusinessstageMapper.insert(saBusinessstageEntity);
                } else {
                    BeanUtils.copyProperties(saBusinessstagePojo, saBusinessstageEntity);
                    this.saBusinessstageMapper.update(saBusinessstageEntity);
                }
            }
        }
        // 4.1 取出更新前的旧数据
        SaBusinessPojo old = this.getBillEntity(saBusinessEntity.getId());

        StringBuilder itemContentSB = new StringBuilder();
        String realName = loginUser.getRealname();

// 对比各字段，新值不为空且与旧值不同时才记录
        if (StringUtils.isNotBlank(saBusinessPojo.getBilltitle())
                && !saBusinessPojo.getBilltitle().equals(old.getBilltitle())) {
            itemContentSB.append(realName)
                    .append("修改了商机标题为:").append(saBusinessPojo.getBilltitle()).append("；");
        }
        if (saBusinessPojo.getBilldate() != null
                && !saBusinessPojo.getBilldate().equals(old.getBilldate())) {
            itemContentSB.append(realName)
                    .append("修改了商机日期为:").append(saBusinessPojo.getBilldate()).append("；");
        }
        if (saBusinessPojo.getBilltaxamount() != null
                && !saBusinessPojo.getBilltaxamount().equals(old.getBilltaxamount())) {
            itemContentSB.append(realName)
                    .append("修改了含税金额为:").append(saBusinessPojo.getBilltaxamount()).append("；");
        }
        if (saBusinessPojo.getBillamount() != null
                && !saBusinessPojo.getBillamount().equals(old.getBillamount())) {
            itemContentSB.append(realName)
                    .append("修改了未税金额为:").append(saBusinessPojo.getBillamount()).append("；");
        }
        if (saBusinessPojo.getContractamount() != null
                && !saBusinessPojo.getContractamount().equals(old.getContractamount())) {
            itemContentSB.append(realName)
                    .append("修改了合同金额为:").append(saBusinessPojo.getContractamount()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getCustname())
                && !saBusinessPojo.getCustname().equals(old.getCustname())) {
            itemContentSB.append(realName)
                    .append("修改了客户名称为:").append(saBusinessPojo.getCustname()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getAddress())
                && !saBusinessPojo.getAddress().equals(old.getAddress())) {
            itemContentSB.append(realName)
                    .append("修改了地址为:").append(saBusinessPojo.getAddress()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getLinkman())
                && !saBusinessPojo.getLinkman().equals(old.getLinkman())) {
            itemContentSB.append(realName)
                    .append("修改了联系人为:").append(saBusinessPojo.getLinkman()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getTel())
                && !saBusinessPojo.getTel().equals(old.getTel())) {
            itemContentSB.append(realName)
                    .append("修改了电话为:").append(saBusinessPojo.getTel()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getSource())
                && !saBusinessPojo.getSource().equals(old.getSource())) {
            itemContentSB.append(realName)
                    .append("修改了商机来源为:").append(saBusinessPojo.getSource()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getSalestype())
                && !saBusinessPojo.getSalestype().equals(old.getSalestype())) {
            itemContentSB.append(realName)
                    .append("修改了销售类型为:").append(saBusinessPojo.getSalestype()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getSalesprogress())
                && !saBusinessPojo.getSalesprogress().equals(old.getSalesprogress())) {
            itemContentSB.append(realName)
                    .append("修改了销售进度为:").append(saBusinessPojo.getSalesprogress()).append("；");
        }
        if (saBusinessPojo.getEstimatedtime() != null
                && !saBusinessPojo.getEstimatedtime().equals(old.getEstimatedtime())) {
            itemContentSB.append(realName)
                    .append("修改了预计成交时间为:").append(saBusinessPojo.getEstimatedtime()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getSummary())
                && !saBusinessPojo.getSummary().equals(old.getSummary())) {
            itemContentSB.append(realName)
                    .append("修改了摘要为:").append(saBusinessPojo.getSummary()).append("；");
        }

// 4.2 如果有变更，再插入跟进记录
        if (itemContentSB.length() > 0) {
            SaFollowviewPojo follow = SaFollowviewPojo.buildFollowview(
                    old.getGroupid(),
                    "Customer",
                    old.getCustname(),
                    "Business.update",
                    itemContentSB.toString(),
                    loginUser
            );
            saFollowviewService.insert(follow);
        }

        //返回Bill实例
        return this.getBillEntity(saBusinessEntity.getId());
    }

    @Override
    @Transactional
    public int delete(String key, LoginUser loginUser) {
        SaBusinessPojo saBusinessPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaBusinessitemPojo> lst = saBusinessPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (SaBusinessitemPojo saBusinessitemPojo : lst) {
                this.saBusinessitemMapper.delete(saBusinessitemPojo.getId());
            }
        }
        //Stage子表处理
        List<SaBusinessstagePojo> stageLst = saBusinessPojo.getStage();
        if (stageLst != null) {
            //循环每个删除Stage子表
            for (SaBusinessstagePojo saBusinessstagePojo : stageLst) {
                this.saBusinessstageMapper.delete(saBusinessstagePojo.getId());
            }
        }
        // 创建跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "删除了商机【" + saBusinessPojo.getRefno() + saBusinessPojo.getBilltitle() + "】";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(saBusinessPojo.getGroupid(),
                "Customer",
                saBusinessPojo.getCustname(),
                "Business.delete",
                itemContent,
                loginUser);
        saFollowviewService.insert(buildFollowview);
        return this.saBusinessMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param saBusinessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaBusinessPojo approval(SaBusinessPojo saBusinessPojo) {
        //主表更改
        SaBusinessEntity saBusinessEntity = new SaBusinessEntity();
        BeanUtils.copyProperties(saBusinessPojo, saBusinessEntity);
        this.saBusinessMapper.approval(saBusinessEntity);
        //返回Bill实例
        return this.getBillEntity(saBusinessEntity.getId());
    }

    private static void cleanNull(SaBusinessPojo saBusinessPojo) {
        if (saBusinessPojo.getRefno() == null) saBusinessPojo.setRefno("");
        if (saBusinessPojo.getBilltype() == null) saBusinessPojo.setBilltype("");
        if (saBusinessPojo.getBilltitle() == null) saBusinessPojo.setBilltitle("");
        if (saBusinessPojo.getBilldate() == null) saBusinessPojo.setBilldate(new Date());
        if (saBusinessPojo.getBilltaxamount() == null) saBusinessPojo.setBilltaxamount(0D);
        if (saBusinessPojo.getBillamount() == null) saBusinessPojo.setBillamount(0D);
        if (saBusinessPojo.getBilltaxtotal() == null) saBusinessPojo.setBilltaxtotal(0D);
        if (saBusinessPojo.getContractamount() == null) saBusinessPojo.setContractamount(0D);
        if (saBusinessPojo.getGroupid() == null) saBusinessPojo.setGroupid("");
        if (saBusinessPojo.getAddress() == null) saBusinessPojo.setAddress("");
        if (saBusinessPojo.getLinkman() == null) saBusinessPojo.setLinkman("");
        if (saBusinessPojo.getTel() == null) saBusinessPojo.setTel("");
        if (saBusinessPojo.getFax() == null) saBusinessPojo.setFax("");
        if (saBusinessPojo.getReporter() == null) saBusinessPojo.setReporter("");
        if (saBusinessPojo.getSource() == null) saBusinessPojo.setSource("");
        if (saBusinessPojo.getBusinesstype() == null) saBusinessPojo.setBusinesstype("");
        if (saBusinessPojo.getSalestype() == null) saBusinessPojo.setSalestype("");
        if (saBusinessPojo.getSalesprogress() == null) saBusinessPojo.setSalesprogress("");
        if (saBusinessPojo.getEstimatedamt() == null) saBusinessPojo.setEstimatedamt("");
        if (saBusinessPojo.getEstimatedtime() == null) saBusinessPojo.setEstimatedtime(new Date());
        if (saBusinessPojo.getMarketing() == null) saBusinessPojo.setMarketing("");
        if (saBusinessPojo.getStatus() == null) saBusinessPojo.setStatus("");
        if (saBusinessPojo.getWorkstage() == null) saBusinessPojo.setWorkstage("");
        if (saBusinessPojo.getStageid() == null) saBusinessPojo.setStageid("");
        if (saBusinessPojo.getStagetext() == null) saBusinessPojo.setStagetext("");
        if (saBusinessPojo.getStageresult() == null) saBusinessPojo.setStageresult(0);
        if (saBusinessPojo.getOperatorid() == null) saBusinessPojo.setOperatorid("");
        if (saBusinessPojo.getOperator() == null) saBusinessPojo.setOperator("");
        if (saBusinessPojo.getSummary() == null) saBusinessPojo.setSummary("");
        if (saBusinessPojo.getCreateby() == null) saBusinessPojo.setCreateby("");
        if (saBusinessPojo.getCreatebyid() == null) saBusinessPojo.setCreatebyid("");
        if (saBusinessPojo.getCreatedate() == null) saBusinessPojo.setCreatedate(new Date());
        if (saBusinessPojo.getLister() == null) saBusinessPojo.setLister("");
        if (saBusinessPojo.getListerid() == null) saBusinessPojo.setListerid("");
        if (saBusinessPojo.getModifydate() == null) saBusinessPojo.setModifydate(new Date());
        if (saBusinessPojo.getAssessor() == null) saBusinessPojo.setAssessor("");
        if (saBusinessPojo.getAssessorid() == null) saBusinessPojo.setAssessorid("");
        if (saBusinessPojo.getAssessdate() == null) saBusinessPojo.setAssessdate(new Date());
        if (saBusinessPojo.getOaflowmark() == null) saBusinessPojo.setOaflowmark(0);
        if (saBusinessPojo.getStatecode() == null) saBusinessPojo.setStatecode("");
        if (saBusinessPojo.getStatedate() == null) saBusinessPojo.setStatedate(new Date());
        if (saBusinessPojo.getCustom1() == null) saBusinessPojo.setCustom1("");
        if (saBusinessPojo.getCustom2() == null) saBusinessPojo.setCustom2("");
        if (saBusinessPojo.getCustom3() == null) saBusinessPojo.setCustom3("");
        if (saBusinessPojo.getCustom4() == null) saBusinessPojo.setCustom4("");
        if (saBusinessPojo.getCustom5() == null) saBusinessPojo.setCustom5("");
        if (saBusinessPojo.getCustom6() == null) saBusinessPojo.setCustom6("");
        if (saBusinessPojo.getCustom7() == null) saBusinessPojo.setCustom7("");
        if (saBusinessPojo.getCustom8() == null) saBusinessPojo.setCustom8("");
        if (saBusinessPojo.getCustom9() == null) saBusinessPojo.setCustom9("");
        if (saBusinessPojo.getCustom10() == null) saBusinessPojo.setCustom10("");
        if (saBusinessPojo.getDeptid() == null) saBusinessPojo.setDeptid("");
        if (saBusinessPojo.getTenantid() == null) saBusinessPojo.setTenantid("");
        if (saBusinessPojo.getTenantname() == null) saBusinessPojo.setTenantname("");
        if (saBusinessPojo.getRevision() == null) saBusinessPojo.setRevision(0);
    }


    @Override
    public void updateOaflowmark(SaBusinessPojo billEntity) {
        this.saBusinessMapper.updateOaflowmark(billEntity);
    }

    @Autowired
    private VelocityEngine velocityEngine;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private OAController OAController;
    @Resource
    private SaJustauthService saJustauthService;
    @Resource
    private JdbcTemplate jdbcTemplate;

    /**
     * 最终版方法签名，只接收 Map，不感知 SaBusinessPojo
     */
    public ApprrecPojo sendApprovalNew(ApprovalRequestDto request, LoginUser loginUser, Map<String, Object> billMap, String tableName) {
        // 步骤 1: 业务前置校验 (对 Map 进行操作)
        Object oaflowmark = billMap.get("oaflowmark");
        if (Objects.equals(1, oaflowmark) || Objects.equals("1", String.valueOf(oaflowmark))) {
            throw new IllegalStateException("该单据已发起OA审批，请勿重复提交");
        }

        // 步骤 2: 获取缓存中的审批模板
        String templateCacheKey = CacheConstants.APPR_CODES_KEY + request.getApprid();
        ApprrecPojo apprrecPojo = saRedisService.getCacheObject(templateCacheKey, ApprrecPojo.class);
        if (apprrecPojo == null) {
            throw new IllegalArgumentException("审批模板不存在或已过期");
        }

        // 步骤 3: 准备模板渲染所需的数据
        ApprovePojo approvePojo = prepareApproveData(request, billMap, loginUser, apprrecPojo.getTemplateid());

        // 步骤 4: 渲染模板并根据需要替换 process 节点
        String finalData = renderTemplate(apprrecPojo.getDatatemp(), approvePojo, request.getProcess());
        apprrecPojo.setDatatemp(finalData);

        // 步骤 5: 填充审批记录对象并存入 Redis
        populateAndCacheRecord(apprrecPojo, request, loginUser);

        // 步骤 6: 触发第三方审批流程
        String type = StringUtils.defaultIfBlank(request.getType(), "wxe");
        if ("wxe".equals(type) || "ding".equals(type)) {
            R r = this.OAController.sendApproval(apprrecPojo.getId(), type);
            if (r.getCode() != 200) {
                throw new RuntimeException("调用第三方审批接口失败: " + r.getMsg());
            }
        }

        // --- 步骤 7: 更新业务单据状态 ---
        // 调用职责单一的更新方法
        updateOaFlowMarkById(tableName, billMap);
        return apprrecPojo;
    }

    /**
     * 最终版更新方法：职责单一，逻辑固定。
     * 为指定表，根据 id 更新 oaflowmark 字段。
     *
     * @param tableName 目标表名
     * @param dataMap   包含 "id" 值的 Map
     */
    private void updateOaFlowMarkById(String tableName, Map<String, Object> dataMap) {
        // 1. 从 Map 中获取 "id" 的值
        Object primaryKeyValue = dataMap.get("id");
        if (primaryKeyValue == null) {
            throw new IllegalArgumentException("更新失败：业务数据中必须包含 'id' 字段。");
        }
        // 2. 构建SQL，所有标识符(表名、列名)均为硬编码，绝对安全。
        String sql = String.format("UPDATE %s SET oaflowmark = ? WHERE id = ?", tableName);
        // 3. 执行更新，将状态值硬编码为 1
        jdbcTemplate.update(sql, 1, primaryKeyValue);
    }

    private ApprovePojo prepareApproveData(ApprovalRequestDto request, Map<String, Object> billMap, LoginUser loginUser, String templateId) {
        ApprovePojo approvePojo = new ApprovePojo();
        approvePojo.setObject(billMap);

        String type = request.getType();
        if (!"oms".equals(type)) {
            SaJustauthPojo justAuth = saJustauthService.getJustauthByUserid(loginUser.getUserid(), type, null);
            if (justAuth == null) {
                throw new IllegalStateException("审批发起人第三方账号信息未找到");
            }
            JustauthPojo justauthPojo = new JustauthPojo();
            org.springframework.beans.BeanUtils.copyProperties(justAuth, justauthPojo);

            approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
            approvePojo.setUserid(justauthPojo.getAuthuuid());
            approvePojo.setModelcode(templateId);
        }
        return approvePojo;
    }

    private String renderTemplate(String templateContent, ApprovePojo approveData, com.fasterxml.jackson.databind.JsonNode processNode) {
        VelocityContext context = new VelocityContext();
        context.put("approvePojo", approveData);
        StringWriter writer = new StringWriter();
        velocityEngine.evaluate(context, writer, "TemplateRenderer", templateContent);
        String renderedJson = writer.toString();

        if (processNode != null && !processNode.isNull()) {
            try {
                JSONObject jsonObject = JSONObject.parseObject(renderedJson);
                // 将JsonNode转换为JSONObject
                String processJsonString = processNode.toString();
                JSONObject processJsonObject = JSONObject.parseObject(processJsonString);
                jsonObject.put("process", processJsonObject);
                return jsonObject.toJSONString();
            } catch (Exception e) {
                throw new IllegalArgumentException("Process参数非标准JSON格式，替换失败", e);
            }
        }
        return renderedJson;
    }


    private void populateAndCacheRecord(ApprrecPojo apprrecPojo, ApprovalRequestDto request, LoginUser loginUser) {
        apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
        apprrecPojo.setApprname("订单审批");
        apprrecPojo.setBillid(request.getKey());
        apprrecPojo.setResultcode("");
        apprrecPojo.setUserid("");
        apprrecPojo.setApprtype("");
        apprrecPojo.setCreateby(loginUser.getRealname());
        apprrecPojo.setCreatebyid(loginUser.getUserid());
        apprrecPojo.setCreatedate(new Date());
        apprrecPojo.setLister(loginUser.getRealname());
        apprrecPojo.setListerid(loginUser.getUserid());
        apprrecPojo.setModifydate(new Date());
        apprrecPojo.setTenantid(loginUser.getTenantid());
        apprrecPojo.setRemark(StringUtils.defaultString(request.getRemark()));
        String cacheKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
        saRedisService.setKeyValue(cacheKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);
    }
}

