package inks.service.sa.crm.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.crm.domain.SaBusinessEntity;
import inks.service.sa.crm.domain.SaBusinessitemEntity;
import inks.service.sa.crm.domain.SaBusinessstageEntity;
import inks.service.sa.crm.domain.pojo.*;
import inks.service.sa.crm.mapper.SaBusinessMapper;
import inks.service.sa.crm.mapper.SaBusinessitemMapper;
import inks.service.sa.crm.mapper.SaBusinessstageMapper;
import inks.service.sa.crm.mapper.SaCustomerMapper;
import inks.service.sa.crm.service.SaBusinessService;
import inks.service.sa.crm.service.SaBusinessitemService;
import inks.service.sa.crm.service.SaBusinessstageService;
import inks.service.sa.crm.service.SaFollowviewService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 商机(SaBusiness)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-20 14:21:30
 */
@Service("saBusinessService")
public class SaBusinessServiceImpl implements SaBusinessService {
    @Resource
    private SaCustomerMapper saCustomerMapper;

    @Resource
    private SaBusinessMapper saBusinessMapper;

    @Resource
    private SaBusinessitemMapper saBusinessitemMapper;
    @Resource
    private SaBusinessstageMapper saBusinessstageMapper;
    @Resource
    private SaBusinessstageService saBusinessstageService;
    /**
     * 服务对象Item
     */
    @Resource
    private SaBusinessitemService saBusinessitemService;
    @Autowired
    private SaFollowviewService saFollowviewService;


    @Override
    public SaBusinessPojo getEntity(String key) {
        return this.saBusinessMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaBusinessitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBusinessitemdetailPojo> lst = saBusinessMapper.getPageList(queryParam);
            PageInfo<SaBusinessitemdetailPojo> pageInfo = new PageInfo<SaBusinessitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaBusinessPojo getBillEntity(String key) {
        try {
            //读取主表
            SaBusinessPojo saBusinessPojo = this.saBusinessMapper.getEntity(key);
            //读取子表
            saBusinessPojo.setItem(saBusinessitemMapper.getList(saBusinessPojo.getId()));
            //读取Stage子表
            saBusinessPojo.setStage(saBusinessstageMapper.getList(saBusinessPojo.getId()));
            return saBusinessPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaBusinessPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBusinessPojo> lst = saBusinessMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表、Stage子表
            for (SaBusinessPojo saBusinessPojo : lst) {
                saBusinessPojo.setItem(saBusinessitemMapper.getList(saBusinessPojo.getId()));
                saBusinessPojo.setStage(saBusinessstageMapper.getList(saBusinessPojo.getId()));
            }
            PageInfo<SaBusinessPojo> pageInfo = new PageInfo<SaBusinessPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaBusinessPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaBusinessPojo> lst = saBusinessMapper.getPageTh(queryParam);
            PageInfo<SaBusinessPojo> pageInfo = new PageInfo<SaBusinessPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saBusinessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaBusinessPojo insert(SaBusinessPojo saBusinessPojo, LoginUser loginUser) {
//初始化NULL字段
        cleanNull(saBusinessPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaBusinessEntity saBusinessEntity = new SaBusinessEntity();
        BeanUtils.copyProperties(saBusinessPojo, saBusinessEntity);
        //设置id和新建日期
        saBusinessEntity.setId(id);
        saBusinessEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saBusinessMapper.insert(saBusinessEntity);
        //Item子表处理
        List<SaBusinessitemPojo> lst = saBusinessPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (SaBusinessitemPojo saBusinessitemPojo : lst) {
                //初始化item的NULL
                SaBusinessitemPojo itemPojo = this.saBusinessitemService.clearNull(saBusinessitemPojo);
                SaBusinessitemEntity saBusinessitemEntity = new SaBusinessitemEntity();
                BeanUtils.copyProperties(itemPojo, saBusinessitemEntity);
                //设置id和Pid
                saBusinessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saBusinessitemEntity.setPid(id);
                saBusinessitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saBusinessitemMapper.insert(saBusinessitemEntity);
            }
        }

        //Stage子表处理
        List<SaBusinessstagePojo> stageLst = saBusinessPojo.getStage();
        if (stageLst != null) {
            //循环每个Stage子表
            for (SaBusinessstagePojo saBusinessstagePojo : stageLst) {
                //初始化Stage的NULL
                SaBusinessstagePojo itemPojo = saBusinessstageService.clearNull(saBusinessstagePojo);
                SaBusinessstageEntity saBusinessstageEntity = new SaBusinessstageEntity();
                BeanUtils.copyProperties(itemPojo, saBusinessstageEntity);
                //设置id和Pid
                saBusinessstageEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saBusinessstageEntity.setPid(id);
                saBusinessstageEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saBusinessstageMapper.insert(saBusinessstageEntity);
            }
        }
        SaBusinessPojo billPojo = this.getBillEntity(saBusinessEntity.getId());
        // 创建客户跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "创建了商机【" + billPojo.getRefno() + billPojo.getBilltitle() + "】";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(billPojo.getGroupid(),
                "Customer",
                billPojo.getCustname(),
                "Business.insert",
                itemContent,
                loginUser);
        saFollowviewService.insert(buildFollowview);
        //返回Bill实例
        return billPojo;

    }

    /**
     * 修改数据
     *
     * @param saBusinessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaBusinessPojo update(SaBusinessPojo saBusinessPojo, LoginUser loginUser) {
        //主表更改
        SaBusinessEntity saBusinessEntity = new SaBusinessEntity();
        BeanUtils.copyProperties(saBusinessPojo, saBusinessEntity);
        this.saBusinessMapper.update(saBusinessEntity);
        if (saBusinessPojo.getItem() != null) {
            //Item子表处理
            List<SaBusinessitemPojo> lst = saBusinessPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saBusinessMapper.getDelItemIds(saBusinessPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.saBusinessitemMapper.delete(lstDelId);
                }
            }
            //循环每个item子表
            for (SaBusinessitemPojo saBusinessitemPojo : lst) {
                SaBusinessitemEntity saBusinessitemEntity = new SaBusinessitemEntity();
                if ("".equals(saBusinessitemPojo.getId()) || saBusinessitemPojo.getId() == null) {
                    //初始化item的NULL
                    SaBusinessitemPojo itemPojo = this.saBusinessitemService.clearNull(saBusinessitemPojo);
                    BeanUtils.copyProperties(itemPojo, saBusinessitemEntity);
                    //设置id和Pid
                    saBusinessitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    saBusinessitemEntity.setPid(saBusinessEntity.getId());  // 主表 id
                    saBusinessitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.saBusinessitemMapper.insert(saBusinessitemEntity);
                } else {
                    BeanUtils.copyProperties(saBusinessitemPojo, saBusinessitemEntity);
                    this.saBusinessitemMapper.update(saBusinessitemEntity);
                }
            }
        }


        //Stage子表处理
        if (saBusinessPojo.getStage() != null) {
            List<SaBusinessstagePojo> stageLst = saBusinessPojo.getStage();
            //获取被删除的Stage
            List<String> lstDelIds = saBusinessMapper.getDelStageIds(saBusinessPojo);
            if (lstDelIds != null) {
                //循环每个删除Stage子表
                for (String lstDelId : lstDelIds) {
                    this.saBusinessstageMapper.delete(lstDelId);
                }
            }
            //循环每个Stage子表
            for (SaBusinessstagePojo saBusinessstagePojo : stageLst) {
                SaBusinessstageEntity saBusinessstageEntity = new SaBusinessstageEntity();
                if ("".equals(saBusinessstagePojo.getId()) || saBusinessstagePojo.getId() == null) {
                    //初始化Stage的NULL
                    SaBusinessstagePojo itemPojo = saBusinessstageService.clearNull(saBusinessstagePojo);
                    BeanUtils.copyProperties(itemPojo, saBusinessstageEntity);
                    //设置id和Pid
                    saBusinessstageEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // stage id
                    saBusinessstageEntity.setPid(saBusinessEntity.getId());  // 主表 id
                    saBusinessstageEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.saBusinessstageMapper.insert(saBusinessstageEntity);
                } else {
                    BeanUtils.copyProperties(saBusinessstagePojo, saBusinessstageEntity);
                    this.saBusinessstageMapper.update(saBusinessstageEntity);
                }
            }
        }
        // 4.1 取出更新前的旧数据
        SaBusinessPojo old = this.getBillEntity(saBusinessEntity.getId());

        StringBuilder itemContentSB = new StringBuilder();
        String realName = loginUser.getRealname();

// 对比各字段，新值不为空且与旧值不同时才记录
        if (StringUtils.isNotBlank(saBusinessPojo.getBilltitle())
                && !saBusinessPojo.getBilltitle().equals(old.getBilltitle())) {
            itemContentSB.append(realName)
                    .append("修改了商机标题为:").append(saBusinessPojo.getBilltitle()).append("；");
        }
        if (saBusinessPojo.getBilldate() != null
                && !saBusinessPojo.getBilldate().equals(old.getBilldate())) {
            itemContentSB.append(realName)
                    .append("修改了商机日期为:").append(saBusinessPojo.getBilldate()).append("；");
        }
        if (saBusinessPojo.getBilltaxamount() != null
                && !saBusinessPojo.getBilltaxamount().equals(old.getBilltaxamount())) {
            itemContentSB.append(realName)
                    .append("修改了含税金额为:").append(saBusinessPojo.getBilltaxamount()).append("；");
        }
        if (saBusinessPojo.getBillamount() != null
                && !saBusinessPojo.getBillamount().equals(old.getBillamount())) {
            itemContentSB.append(realName)
                    .append("修改了未税金额为:").append(saBusinessPojo.getBillamount()).append("；");
        }
        if (saBusinessPojo.getContractamount() != null
                && !saBusinessPojo.getContractamount().equals(old.getContractamount())) {
            itemContentSB.append(realName)
                    .append("修改了合同金额为:").append(saBusinessPojo.getContractamount()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getCustname())
                && !saBusinessPojo.getCustname().equals(old.getCustname())) {
            itemContentSB.append(realName)
                    .append("修改了客户名称为:").append(saBusinessPojo.getCustname()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getAddress())
                && !saBusinessPojo.getAddress().equals(old.getAddress())) {
            itemContentSB.append(realName)
                    .append("修改了地址为:").append(saBusinessPojo.getAddress()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getLinkman())
                && !saBusinessPojo.getLinkman().equals(old.getLinkman())) {
            itemContentSB.append(realName)
                    .append("修改了联系人为:").append(saBusinessPojo.getLinkman()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getTel())
                && !saBusinessPojo.getTel().equals(old.getTel())) {
            itemContentSB.append(realName)
                    .append("修改了电话为:").append(saBusinessPojo.getTel()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getSource())
                && !saBusinessPojo.getSource().equals(old.getSource())) {
            itemContentSB.append(realName)
                    .append("修改了商机来源为:").append(saBusinessPojo.getSource()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getSalestype())
                && !saBusinessPojo.getSalestype().equals(old.getSalestype())) {
            itemContentSB.append(realName)
                    .append("修改了销售类型为:").append(saBusinessPojo.getSalestype()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getSalesprogress())
                && !saBusinessPojo.getSalesprogress().equals(old.getSalesprogress())) {
            itemContentSB.append(realName)
                    .append("修改了销售进度为:").append(saBusinessPojo.getSalesprogress()).append("；");
        }
        if (saBusinessPojo.getEstimatedtime() != null
                && !saBusinessPojo.getEstimatedtime().equals(old.getEstimatedtime())) {
            itemContentSB.append(realName)
                    .append("修改了预计成交时间为:").append(saBusinessPojo.getEstimatedtime()).append("；");
        }
        if (StringUtils.isNotBlank(saBusinessPojo.getSummary())
                && !saBusinessPojo.getSummary().equals(old.getSummary())) {
            itemContentSB.append(realName)
                    .append("修改了摘要为:").append(saBusinessPojo.getSummary()).append("；");
        }

// 4.2 如果有变更，再插入跟进记录
        if (itemContentSB.length() > 0) {
            SaFollowviewPojo follow = SaFollowviewPojo.buildFollowview(
                    old.getGroupid(),
                    "Customer",
                    old.getCustname(),
                    "Business.update",
                    itemContentSB.toString(),
                    loginUser
            );
            saFollowviewService.insert(follow);
        }

        //返回Bill实例
        return this.getBillEntity(saBusinessEntity.getId());
    }

    @Override
    @Transactional
    public int delete(String key, LoginUser loginUser) {
        SaBusinessPojo saBusinessPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaBusinessitemPojo> lst = saBusinessPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (SaBusinessitemPojo saBusinessitemPojo : lst) {
                this.saBusinessitemMapper.delete(saBusinessitemPojo.getId());
            }
        }
        //Stage子表处理
        List<SaBusinessstagePojo> stageLst = saBusinessPojo.getStage();
        if (stageLst != null) {
            //循环每个删除Stage子表
            for (SaBusinessstagePojo saBusinessstagePojo : stageLst) {
                this.saBusinessstageMapper.delete(saBusinessstagePojo.getId());
            }
        }
        // 创建跟进记录 IsAuto=1 为自动生成
        String itemContent = loginUser.getRealname() + "删除了商机【" + saBusinessPojo.getRefno() + saBusinessPojo.getBilltitle() + "】";
        SaFollowviewPojo buildFollowview = SaFollowviewPojo.buildFollowview(saBusinessPojo.getGroupid(),
                "Customer",
                saBusinessPojo.getCustname(),
                "Business.delete",
                itemContent,
                loginUser);
        saFollowviewService.insert(buildFollowview);
        return this.saBusinessMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param saBusinessPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaBusinessPojo approval(SaBusinessPojo saBusinessPojo) {
        //主表更改
        SaBusinessEntity saBusinessEntity = new SaBusinessEntity();
        BeanUtils.copyProperties(saBusinessPojo, saBusinessEntity);
        this.saBusinessMapper.approval(saBusinessEntity);
        //返回Bill实例
        return this.getBillEntity(saBusinessEntity.getId());
    }

    private static void cleanNull(SaBusinessPojo saBusinessPojo) {
        if (saBusinessPojo.getRefno() == null) saBusinessPojo.setRefno("");
        if (saBusinessPojo.getBilltype() == null) saBusinessPojo.setBilltype("");
        if (saBusinessPojo.getBilltitle() == null) saBusinessPojo.setBilltitle("");
        if (saBusinessPojo.getBilldate() == null) saBusinessPojo.setBilldate(new Date());
        if (saBusinessPojo.getBilltaxamount() == null) saBusinessPojo.setBilltaxamount(0D);
        if (saBusinessPojo.getBillamount() == null) saBusinessPojo.setBillamount(0D);
        if (saBusinessPojo.getBilltaxtotal() == null) saBusinessPojo.setBilltaxtotal(0D);
        if (saBusinessPojo.getContractamount() == null) saBusinessPojo.setContractamount(0D);
        if (saBusinessPojo.getGroupid() == null) saBusinessPojo.setGroupid("");
        if (saBusinessPojo.getAddress() == null) saBusinessPojo.setAddress("");
        if (saBusinessPojo.getLinkman() == null) saBusinessPojo.setLinkman("");
        if (saBusinessPojo.getTel() == null) saBusinessPojo.setTel("");
        if (saBusinessPojo.getFax() == null) saBusinessPojo.setFax("");
        if (saBusinessPojo.getReporter() == null) saBusinessPojo.setReporter("");
        if (saBusinessPojo.getSource() == null) saBusinessPojo.setSource("");
        if (saBusinessPojo.getBusinesstype() == null) saBusinessPojo.setBusinesstype("");
        if (saBusinessPojo.getSalestype() == null) saBusinessPojo.setSalestype("");
        if (saBusinessPojo.getSalesprogress() == null) saBusinessPojo.setSalesprogress("");
        if (saBusinessPojo.getEstimatedamt() == null) saBusinessPojo.setEstimatedamt("");
        if (saBusinessPojo.getEstimatedtime() == null) saBusinessPojo.setEstimatedtime(new Date());
        if (saBusinessPojo.getMarketing() == null) saBusinessPojo.setMarketing("");
        if (saBusinessPojo.getStatus() == null) saBusinessPojo.setStatus("");
        if (saBusinessPojo.getWorkstage() == null) saBusinessPojo.setWorkstage("");
        if (saBusinessPojo.getStageid() == null) saBusinessPojo.setStageid("");
        if (saBusinessPojo.getStagetext() == null) saBusinessPojo.setStagetext("");
        if (saBusinessPojo.getStageresult() == null) saBusinessPojo.setStageresult(0);
        if (saBusinessPojo.getOperatorid() == null) saBusinessPojo.setOperatorid("");
        if (saBusinessPojo.getOperator() == null) saBusinessPojo.setOperator("");
        if (saBusinessPojo.getSummary() == null) saBusinessPojo.setSummary("");
        if (saBusinessPojo.getCreateby() == null) saBusinessPojo.setCreateby("");
        if (saBusinessPojo.getCreatebyid() == null) saBusinessPojo.setCreatebyid("");
        if (saBusinessPojo.getCreatedate() == null) saBusinessPojo.setCreatedate(new Date());
        if (saBusinessPojo.getLister() == null) saBusinessPojo.setLister("");
        if (saBusinessPojo.getListerid() == null) saBusinessPojo.setListerid("");
        if (saBusinessPojo.getModifydate() == null) saBusinessPojo.setModifydate(new Date());
        if (saBusinessPojo.getAssessor() == null) saBusinessPojo.setAssessor("");
        if (saBusinessPojo.getAssessorid() == null) saBusinessPojo.setAssessorid("");
        if (saBusinessPojo.getAssessdate() == null) saBusinessPojo.setAssessdate(new Date());
        if(saBusinessPojo.getOaflowmark()==null) saBusinessPojo.setOaflowmark(0);
        if(saBusinessPojo.getStatecode()==null) saBusinessPojo.setStatecode("");
        if (saBusinessPojo.getStatedate() == null) saBusinessPojo.setStatedate(new Date());
        if (saBusinessPojo.getCustom1() == null) saBusinessPojo.setCustom1("");
        if (saBusinessPojo.getCustom2() == null) saBusinessPojo.setCustom2("");
        if (saBusinessPojo.getCustom3() == null) saBusinessPojo.setCustom3("");
        if (saBusinessPojo.getCustom4() == null) saBusinessPojo.setCustom4("");
        if (saBusinessPojo.getCustom5() == null) saBusinessPojo.setCustom5("");
        if (saBusinessPojo.getCustom6() == null) saBusinessPojo.setCustom6("");
        if (saBusinessPojo.getCustom7() == null) saBusinessPojo.setCustom7("");
        if (saBusinessPojo.getCustom8() == null) saBusinessPojo.setCustom8("");
        if (saBusinessPojo.getCustom9() == null) saBusinessPojo.setCustom9("");
        if (saBusinessPojo.getCustom10() == null) saBusinessPojo.setCustom10("");
        if (saBusinessPojo.getDeptid() == null) saBusinessPojo.setDeptid("");
        if (saBusinessPojo.getTenantid() == null) saBusinessPojo.setTenantid("");
        if (saBusinessPojo.getTenantname() == null) saBusinessPojo.setTenantname("");
        if (saBusinessPojo.getRevision() == null) saBusinessPojo.setRevision(0);
    }


    @Override
    public void updateOaflowmark(SaBusinessPojo billEntity) {
        this.saBusinessMapper.updateOaflowmark(billEntity);
    }
}
