package inks.service.sa.crm.config;

import org.apache.velocity.app.VelocityEngine;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

// --- Spring 配置 (一次性) ---
@Configuration
public class TemplateEngineConfig {
    @Bean
    public VelocityEngine velocityEngine() {
        VelocityEngine ve = new VelocityEngine();
        // 可根据需要设置资源加载器等属性
        // Properties p = new Properties();
        // p.setProperty("resource.loader.file.path", "/path/to/templates");
        ve.init();
        return ve;
    }
}
