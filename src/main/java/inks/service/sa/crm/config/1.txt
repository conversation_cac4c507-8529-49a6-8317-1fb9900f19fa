{
  "creator_userid": "${approvePojo.creatoruserid}",
  "template_id": "${approvePojo.modelcode}",
  "use_template_approver": 0,
  "approver": [{
    "attr": 1,
    "userid": ["XuPuTi"]
  }],
  #if (${approvePojo.notifyer})
"notifyer": ["${approvePojo.notifyer}"],
#end "notify_type": 1,
"apply_data": {
"contents": [
{
"control": "Text",
"id": "Text-1750064610710",
"value": {
"text": "${approvePojo.object.custname}"
}
},
{
"control": "Text",
"id": "Text-1750215464516",
"value": {
"text": "${approvePojo.object.billtitle}"
}
},
{
"control": "Text",
"id": "Text-1750215481931",
"value": {
"text": "${approvePojo.object.billtype}"
}
},
{
"control": "Text",
"id": "Text-1750215517674",
"value": {
"text": "${approvePojo.object.address}"
}
},
{
"control": "Table",
"id": "Table-1750215560491",
"value": {
"children": [
#foreach($object in $approvePojo.object.item)
{
"list": [{
"control": "Text",
"id": "Text-1750216608196",
"value": {
"text": "${object.goodsuid}"
}
},
{
"control": "Text",
"id": "Text-1750215576642",
"value": {
"text": "${object.goodsname}"
}
}
{
"control": "Text",
"id": "Text-1750215602100",
"value": {
"text": "${object.goodsspec}"
}
}
,
{
"control": "Text",
"id": "Text-1750215625275",
"value": {
"text": "${object.quantity}"
}
}
,
{
"control": "Text",
"id": "Text-1750215641215",
"value": {
"text": "${object.taxprice}"
}
}
,
{
"control": "Text",
"id": "Text-1750215650517,
"value": {
"text": "${object.taxamount}"
}
}
]
}
#if($foreach.hasNext),#end
#end
]
}

}
]
}
}