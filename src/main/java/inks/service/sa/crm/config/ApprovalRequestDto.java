package inks.service.sa.crm.config;

// --- 1. DTO (Data Transfer Object) ---
// 用于替代 String json，使代码类型安全、易于维护和校验。
public class ApprovalRequestDto {
    private String key;
    private String apprid;
    private String type;
    private String remark;
    private String process; // 保持为 JSON 字符串

    // --- Get<PERSON> and Setters ---
    public String getKey() { return key; }
    public void setKey(String key) { this.key = key; }
    public String getApprid() { return apprid; }
    public void setApprid(String apprid) { this.apprid = apprid; }
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }
    public String getRemark() { return remark; }
    public void setRemark(String remark) { this.remark = remark; }
    public String getProcess() { return process; }
    public void setProcess(String process) { this.process = process; }
}