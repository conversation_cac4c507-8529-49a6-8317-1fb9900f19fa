# 开发规范和规则

- S07M05B1Controller.sendapprovel方法已修改为POST请求，支持通过JSON传入process参数。如果传入process参数，直接使用process替换DataTemp，否则按原逻辑处理。参数格式：{"key":"","apprid":"","type":"","remark":"","process":""}
- S07M05B1Controller.sendapprovel方法修改逻辑：先按原逻辑处理Velocity模板，如果传入process参数，则解析生成的JSON模板，将其中的process部分替换为传入的process对象。process参数应为JSON字符串格式，包含node_list等审批流程信息。
- ApprovalRequestDto中的process字段已修改为JsonNode类型，支持直接接收JSON对象。SaBusinessServiceImpl的renderTemplate方法也相应修改，支持处理JsonNode类型的process参数。现在可以直接传入process对象而不需要字符串格式。
- 已重构justapprovel方法，采用新架构模式：使用ApprovalCallbackDto接收参数，业务逻辑委托给SaBusinessService.handleApprovalCallback方法处理。新方法更简洁、可维护性更强。
- 已重构handleApprovalCallback方法为通用实现：Controller层查询实体并转换为Map传入Service，Service层使用JdbcTemplate进行通用的数据库更新操作，不再依赖特定的实体类方法。支持任意表名的审批回调处理。
