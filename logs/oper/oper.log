{"@timestamp":"2025-07-28T13:10:40.231+08:00","@version":"1","message":"The following profiles are active: dev","logger_name":"inks.service.sa.crm.InksServiceSaCrmApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:40.249+08:00","@version":"1","message":"Loading source class inks.service.sa.crm.InksServiceSaCrmApplication,class org.springframework.cloud.bootstrap.BootstrapApplicationListener$BootstrapMarkerConfiguration","logger_name":"org.springframework.boot.SpringApplication","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:40.286+08:00","@version":"1","message":"Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6ee8dcd3","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:40.590+08:00","@version":"1","message":"Found key 'inks.redisType' in PropertySource 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/'' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:40.591+08:00","@version":"1","message":"Found key 'inks.redisType' in PropertySource 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/'' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:41.398+08:00","@version":"1","message":"Found key 'inks.redisType' in PropertySource 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/'' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:41.399+08:00","@version":"1","message":"Found key 'inks.redisType' in PropertySource 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/'' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:41.455+08:00","@version":"1","message":"Found key 'inks.redisType' in PropertySource 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/'' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:42.180+08:00","@version":"1","message":"Found key 'inks.redisType' in PropertySource 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/'' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:42.571+08:00","@version":"1","message":"@EnableAutoConfiguration was declared on a class in the package 'inks.sa.common.core,inks.service.sa.crm'. Automatic @Repository and @Entity scanning is enabled.","logger_name":"org.springframework.boot.autoconfigure.AutoConfigurationPackages","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:42.980+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saAttachmentMapper' and 'inks.sa.common.core.mapper.SaAttachmentMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.982+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saAuthcodeMapper' and 'inks.sa.common.core.mapper.SaAuthcodeMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.983+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saBillcodeMapper' and 'inks.sa.common.core.mapper.SaBillcodeMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.983+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saBillexpressionMapper' and 'inks.sa.common.core.mapper.SaBillexpressionMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.983+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saBillgroupMapper' and 'inks.sa.common.core.mapper.SaBillgroupMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.984+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saCompanyMapper' and 'inks.sa.common.core.mapper.SaCompanyMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.984+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saConfigMapper' and 'inks.sa.common.core.mapper.SaConfigMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.984+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saDeptMapper' and 'inks.sa.common.core.mapper.SaDeptMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.984+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saDeptuserMapper' and 'inks.sa.common.core.mapper.SaDeptuserMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.985+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'sadgformatitemMapper' and 'inks.sa.common.core.mapper.SadgformatitemMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.985+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'sadgformatMapper' and 'inks.sa.common.core.mapper.SadgformatMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.985+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saDictitemMapper' and 'inks.sa.common.core.mapper.SaDictitemMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.985+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saDictMapper' and 'inks.sa.common.core.mapper.SaDictMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.985+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saDirruleMapper' and 'inks.sa.common.core.mapper.SaDirruleMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.986+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saFilelogMapper' and 'inks.sa.common.core.mapper.SaFilelogMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.986+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saformcustomMapper' and 'inks.sa.common.core.mapper.SaformcustomMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.986+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saIndeximgMapper' and 'inks.sa.common.core.mapper.SaIndeximgMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.986+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saJustauthMapper' and 'inks.sa.common.core.mapper.SaJustauthMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.986+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saLoginlogMapper' and 'inks.sa.common.core.mapper.SaLoginlogMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.987+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saLogMapper' and 'inks.sa.common.core.mapper.SaLogMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.987+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saMenuappMapper' and 'inks.sa.common.core.mapper.SaMenuappMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.987+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saMenuwebMapper' and 'inks.sa.common.core.mapper.SaMenuwebMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.987+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saNoticeMapper' and 'inks.sa.common.core.mapper.SaNoticeMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.988+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saOperlogMapper' and 'inks.sa.common.core.mapper.SaOperlogMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.988+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saPermcodeMapper' and 'inks.sa.common.core.mapper.SaPermcodeMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.988+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saPermissionMapper' and 'inks.sa.common.core.mapper.SaPermissionMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.988+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saRedisMapper' and 'inks.sa.common.core.mapper.SaRedisMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.989+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saReportsMapper' and 'inks.sa.common.core.mapper.SaReportsMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.989+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saRoleMapper' and 'inks.sa.common.core.mapper.SaRoleMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.989+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saRolemenuappMapper' and 'inks.sa.common.core.mapper.SaRolemenuappMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.990+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saRolemenuwebMapper' and 'inks.sa.common.core.mapper.SaRolemenuwebMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.990+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saScenefieldMapper' and 'inks.sa.common.core.mapper.SaScenefieldMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.990+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saSceneMapper' and 'inks.sa.common.core.mapper.SaSceneMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.990+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saUserMapper' and 'inks.sa.common.core.mapper.SaUserMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.990+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saUserroleMapper' and 'inks.sa.common.core.mapper.SaUserroleMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.990+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saValidatorMapper' and 'inks.sa.common.core.mapper.SaValidatorMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.990+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saWarningMapper' and 'inks.sa.common.core.mapper.SaWarningMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.991+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saWarninguserMapper' and 'inks.sa.common.core.mapper.SaWarninguserMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.991+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'saWebnavMapper' and 'inks.sa.common.core.mapper.SaWebnavMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.991+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'sqlUtilMapper' and 'inks.sa.common.core.mapper.SqlUtilMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:42.991+08:00","@version":"1","message":"Skipping MapperFactoryBean with name 'utsWxeapprrecMapper' and 'inks.sa.common.core.mapper.UtsWxeapprrecMapper' mapperInterface. Bean already defined with the same name!","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-28T13:10:44.180+08:00","@version":"1","message":"Code archive: C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot\\2.5.3\\spring-boot-2.5.3.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:44.181+08:00","@version":"1","message":"Code archive: C:\\Users\\<USER>\\.m2\\repository\\org\\springframework\\boot\\spring-boot\\2.5.3\\spring-boot-2.5.3.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:44.181+08:00","@version":"1","message":"None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:44.214+08:00","@version":"1","message":"Tomcat initialized with port(s): 10657 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:44.228+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:44.228+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.50]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:44.469+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:44.470+08:00","@version":"1","message":"Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]","logger_name":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:44.470+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 4184 ms","logger_name":"org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:44.565+08:00","@version":"1","message":"Mapping filters: filterRegistrationBean urls=[/*] order=-2147483648, inks.sa.common.core.config.SwaggerAuthConfig urls=[/swagger-ui.html, /webjars/*, /swagger-resources/*, /v2/api-docs/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, inksConfigFilter urls=[/*] order=-2147483548, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, swaggerAuthConfig urls=[/*] order=2147483647","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:44.565+08:00","@version":"1","message":"Mapping servlets: dispatcherServlet urls=[/]","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:44.597+08:00","@version":"1","message":"Filter 'requestContextFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:44.597+08:00","@version":"1","message":"Filter 'corsFilter' configured for use","logger_name":"org.springframework.web.filter.CorsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:44.598+08:00","@version":"1","message":"InksConfigFilter初始化完成","logger_name":"inks.sa.common.core.config.InksConfigFilter","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:44.598+08:00","@version":"1","message":"Filter 'characterEncodingFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:44.598+08:00","@version":"1","message":"Filter 'formContentFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedFormContentFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:45.936+08:00","@version":"1","message":"HikariPool-1 - Starting...","logger_name":"com.zaxxer.hikari.HikariDataSource","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:45.961+08:00","@version":"1","message":"HikariPool-1 - Start completed.","logger_name":"com.zaxxer.hikari.HikariDataSource","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:49.258+08:00","@version":"1","message":"Found key 'inks.redisType' in PropertySource 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/'' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:49.348+08:00","@version":"1","message":"Found key 'inks.feign.UtsUrl' in PropertySource 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/'' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:49.360+08:00","@version":"1","message":"Merge executor service initialized for AllFileController.","logger_name":"inks.sa.common.core.config.oss.service.FileController","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:49.464+08:00","@version":"1","message":"MinIO sharding environment initialized. Bucket: 'inkscrm'","logger_name":"inks.sa.common.core.config.oss.service.FileController","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:49.511+08:00","@version":"1","message":"Found key 'inks.feign.UtsUrl' in PropertySource 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/'' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:49.530+08:00","@version":"1","message":"Merge executor service initialized for AllFileController.","logger_name":"inks.sa.common.core.config.oss.service.FileController","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:49.542+08:00","@version":"1","message":"MinIO sharding environment initialized. Bucket: 'inkscrm'","logger_name":"inks.sa.common.core.config.oss.service.FileController","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:49.749+08:00","@version":"1","message":"Found key 'inks.feign.GrfUrl' in PropertySource 'Config resource 'class path resource [application-dev.yml]' via location 'optional:classpath:/'' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:50.051+08:00","@version":"1","message":"Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'","logger_name":"org.springframework.boot.admin.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:50.942+08:00","@version":"1","message":"[Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].","logger_name":"com.alibaba.cloud.sentinel.SentinelWebAutoConfiguration","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:51.063+08:00","@version":"1","message":"648 mappings in 'requestMappingHandlerMapping'","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:51.263+08:00","@version":"1","message":"Patterns [/flyway-progress-ws] in 'webSocketHandlerMapping'","logger_name":"org.springframework.web.socket.server.support.WebSocketHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:51.753+08:00","@version":"1","message":"ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:51.858+08:00","@version":"1","message":"Patterns [/webjars/**, /**, /swagger-ui.html] in 'resourceHandlerMapping'","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:51.877+08:00","@version":"1","message":"ControllerAdvice beans: 2 @ExceptionHandler, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:54.932+08:00","@version":"1","message":"Tomcat started on port(s): 10657 (http) with context path ''","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:59.257+08:00","@version":"1","message":"\r\n\r\n\r\n============================\r\nCONDITIONS EVALUATION REPORT\r\n============================\r\n\r\n\r\nPositive matches:\r\n-----------------\r\n\r\n   AliYunOSSConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.aliyun.oss.OSS' (OnClassCondition)\r\n\r\n   AopAutoConfiguration matched:\r\n      - @ConditionalOnProperty (spring.aop.auto=true) matched (OnPropertyCondition)\r\n\r\n   AopAutoConfiguration.AspectJAutoProxyingConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.aspectj.weaver.Advice' (OnClassCondition)\r\n\r\n   AopAutoConfiguration.AspectJAutoProxyingConfiguration.CglibAutoProxyConfiguration matched:\r\n      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)\r\n\r\n   AutoServiceRegistrationAutoConfiguration matched:\r\n      - @ConditionalOnProperty (spring.cloud.service-registry.auto-registration.enabled) matched (OnPropertyCondition)\r\n\r\n   AutoServiceRegistrationConfiguration matched:\r\n      - @ConditionalOnProperty (spring.cloud.service-registry.auto-registration.enabled) matched (OnPropertyCondition)\r\n\r\n   CacheAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)\r\n      - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) found bean 'cacheInterceptor'; @ConditionalOnMissingBean (names: cacheResolver types: org.springframework.cache.CacheManager; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   CacheAutoConfiguration#cacheManagerCustomizers matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.cache.CacheManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   CompatibilityVerifierAutoConfiguration matched:\r\n      - @ConditionalOnProperty (spring.cloud.compatibility-verifier.enabled) matched (OnPropertyCondition)\r\n\r\n   ConfigurationPropertiesRebinderAutoConfiguration matched:\r\n      - @ConditionalOnBean (types: org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor; SearchStrategy: all) found bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor' (OnBeanCondition)\r\n\r\n   ConfigurationPropertiesRebinderAutoConfiguration#configurationPropertiesBeans matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.context.properties.ConfigurationPropertiesBeans; SearchStrategy: current) did not find any beans (OnBeanCondition)\r\n\r\n   ConfigurationPropertiesRebinderAutoConfiguration#configurationPropertiesRebinder matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.context.properties.ConfigurationPropertiesRebinder; SearchStrategy: current) did not find any beans (OnBeanCondition)\r\n\r\n   DataSourceAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)\r\n      - @ConditionalOnMissingBean (types: io.r2dbc.spi.ConnectionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   DataSourceAutoConfiguration.PooledDataSourceConfiguration matched:\r\n      - AnyNestedCondition 1 matched 1 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) did not find property 'type' (DataSourceAutoConfiguration.PooledDataSourceCondition)\r\n      - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   DataSourceConfiguration.Hikari matched:\r\n      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)\r\n      - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) matched (OnPropertyCondition)\r\n      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   DataSourceInitializationConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.jdbc.datasource.init.DatabasePopulator' (OnClassCondition)\r\n      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource'; @ConditionalOnMissingBean (types: org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   DataSourceJmxConfiguration matched:\r\n      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)\r\n\r\n   DataSourceJmxConfiguration.Hikari matched:\r\n      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)\r\n      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)\r\n\r\n   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)\r\n\r\n   DataSourceTransactionManagerAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.TransactionManager' (OnClassCondition)\r\n\r\n   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration matched:\r\n      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)\r\n\r\n   DataSourceTransactionManagerAutoConfiguration.JdbcTransactionManagerConfiguration#transactionManager matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   DispatcherServletAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n\r\n   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:\r\n      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)\r\n      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)\r\n\r\n   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:\r\n      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)\r\n      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)\r\n\r\n   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:\r\n      - @ConditionalOnBean (names: dispatcherServlet types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)\r\n\r\n   EasyPoiAutoConfiguration matched:\r\n      - @ConditionalOnProperty (easy.poi.base.enable) matched (OnPropertyCondition)\r\n\r\n   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:\r\n      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)\r\n\r\n   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)\r\n\r\n   ErrorMvcAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n\r\n   ErrorMvcAutoConfiguration#basicErrorController matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)\r\n\r\n   ErrorMvcAutoConfiguration#errorAttributes matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)\r\n\r\n   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:\r\n      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.ErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:\r\n      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)\r\n      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)\r\n\r\n   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:\r\n      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   FeignAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'feign.Feign' (OnClassCondition)\r\n\r\n   FeignAutoConfiguration.DefaultFeignTargeterConfiguration matched:\r\n      - AnyNestedCondition 1 matched 1 did not; NestedCondition on FeignCircuitBreakerDisabledConditions.CircuitBreakerDisabled @ConditionalOnProperty (feign.circuitbreaker.enabled=false) matched; NestedCondition on FeignCircuitBreakerDisabledConditions.CircuitBreakerClassMissing @ConditionalOnMissingClass found unwanted class 'org.springframework.cloud.client.circuitbreaker.CircuitBreaker' (FeignCircuitBreakerDisabledConditions)\r\n\r\n   FeignAutoConfiguration.DefaultFeignTargeterConfiguration#feignTargeter matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.openfeign.Targeter; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   GsonAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.google.gson.Gson' (OnClassCondition)\r\n\r\n   GsonAutoConfiguration#gson matched:\r\n      - @ConditionalOnMissingBean (types: com.google.gson.Gson; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   GsonAutoConfiguration#gsonBuilder matched:\r\n      - @ConditionalOnMissingBean (types: com.google.gson.GsonBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   GsonHttpMessageConvertersConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.google.gson.Gson' (OnClassCondition)\r\n\r\n   HttpClientConfiguration.ApacheHttpClientConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.apache.http.client.HttpClient' (OnClassCondition)\r\n      - @ConditionalOnProperty (spring.cloud.httpclientfactories.apache.enabled) matched (OnPropertyCondition)\r\n\r\n   HttpClientConfiguration.ApacheHttpClientConfiguration#apacheHttpClientBuilder matched:\r\n      - @ConditionalOnMissingBean (types: org.apache.http.impl.client.HttpClientBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   HttpClientConfiguration.ApacheHttpClientConfiguration#apacheHttpClientFactory matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.commons.httpclient.ApacheHttpClientFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   HttpClientConfiguration.ApacheHttpClientConfiguration#connManFactory matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.commons.httpclient.ApacheHttpClientConnectionManagerFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   HttpClientConfiguration.OkHttpClientConfiguration matched:\r\n      - @ConditionalOnClass found required class 'okhttp3.OkHttpClient' (OnClassCondition)\r\n      - @ConditionalOnProperty (spring.cloud.httpclientfactories.ok.enabled) matched (OnPropertyCondition)\r\n\r\n   HttpClientConfiguration.OkHttpClientConfiguration#connPoolFactory matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.commons.httpclient.OkHttpClientConnectionPoolFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   HttpClientConfiguration.OkHttpClientConfiguration#okHttpClientBuilder matched:\r\n      - @ConditionalOnMissingBean (types: okhttp3.OkHttpClient$Builder; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   HttpClientConfiguration.OkHttpClientConfiguration#okHttpClientFactory matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.commons.httpclient.OkHttpClientFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   HttpEncodingAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n      - @ConditionalOnProperty (server.servlet.encoding.enabled) matched (OnPropertyCondition)\r\n\r\n   HttpEncodingAutoConfiguration#characterEncodingFilter matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   HttpMessageConvertersAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)\r\n      - NoneNestedConditions 0 matched 1 did not; NestedCondition on HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (HttpMessageConvertersAutoConfiguration.NotReactiveWebApplicationCondition)\r\n\r\n   HttpMessageConvertersAutoConfiguration#messageConverters matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)\r\n\r\n   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   JacksonAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)\r\n\r\n   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)\r\n\r\n   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)\r\n\r\n   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)\r\n\r\n   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:\r\n      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)\r\n\r\n   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:\r\n      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)\r\n      - @ConditionalOnProperty (spring.mvc.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)\r\n      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)\r\n\r\n   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter ignored: org.springframework.hateoas.server.mvc.TypeConstrainedMappingJackson2HttpMessageConverter,org.springframework.data.rest.webmvc.alps.AlpsJsonHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)\r\n      - @ConditionalOnBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) found bean 'jacksonObjectMapperBuilder' (OnBeanCondition)\r\n\r\n   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration#mappingJackson2XmlHttpMessageConverter matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.http.converter.xml.MappingJackson2XmlHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   JdbcTemplateAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)\r\n      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)\r\n\r\n   JdbcTemplateConfiguration matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   JmxAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)\r\n      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)\r\n\r\n   JmxAutoConfiguration#mbeanExporter matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.MBeanExporter; SearchStrategy: current) did not find any beans (OnBeanCondition)\r\n\r\n   JmxAutoConfiguration#mbeanServer matched:\r\n      - @ConditionalOnMissingBean (types: javax.management.MBeanServer; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   JmxAutoConfiguration#objectNamingStrategy matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.naming.ObjectNamingStrategy; SearchStrategy: current) did not find any beans (OnBeanCondition)\r\n\r\n   LettuceConnectionConfiguration matched:\r\n      - @ConditionalOnClass found required class 'io.lettuce.core.RedisClient' (OnClassCondition)\r\n      - @ConditionalOnProperty (spring.redis.client-type=lettuce) matched (OnPropertyCondition)\r\n\r\n   LettuceConnectionConfiguration#lettuceClientResources matched:\r\n      - @ConditionalOnMissingBean (types: io.lettuce.core.resource.ClientResources; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   LettuceConnectionConfiguration#redisConnectionFactory matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   LifecycleAutoConfiguration#defaultLifecycleProcessor matched:\r\n      - @ConditionalOnMissingBean (names: lifecycleProcessor; SearchStrategy: current) did not find any beans (OnBeanCondition)\r\n\r\n   LifecycleMvcEndpointAutoConfiguration#environmentManager matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.context.environment.EnvironmentManager; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   MinioOSSConfiguration matched:\r\n      - @ConditionalOnClass found required class 'io.minio.MinioClient' (OnClassCondition)\r\n\r\n   MultipartAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'javax.servlet.MultipartConfigElement' (OnClassCondition)\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)\r\n\r\n   MultipartAutoConfiguration#multipartConfigElement matched:\r\n      - @ConditionalOnMissingBean (types: javax.servlet.MultipartConfigElement,org.springframework.web.multipart.commons.CommonsMultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   MultipartAutoConfiguration#multipartResolver matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   MybatisAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)\r\n      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)\r\n\r\n   MybatisAutoConfiguration#sqlSessionFactory matched:\r\n      - @ConditionalOnMissingBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   MybatisAutoConfiguration#sqlSessionTemplate matched:\r\n      - @ConditionalOnMissingBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   MybatisLanguageDriverAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)\r\n\r\n   NamedParameterJdbcTemplateConfiguration matched:\r\n      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a primary bean from beans 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   NettyAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'io.netty.util.NettyRuntime' (OnClassCondition)\r\n\r\n   PageHelperAutoConfiguration matched:\r\n      - @ConditionalOnBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) found bean 'sqlSessionFactory' (OnBeanCondition)\r\n\r\n   PersistenceExceptionTranslationAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)\r\n\r\n   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:\r\n      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)\r\n      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   ReactiveSentinelCircuitBreakerAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'reactor.core.publisher.Mono', 'reactor.core.publisher.Flux' (OnClassCondition)\r\n      - @ConditionalOnProperty (spring.cloud.circuitbreaker.sentinel.enabled=true) matched (OnPropertyCondition)\r\n\r\n   ReactiveSentinelCircuitBreakerAutoConfiguration#reactiveSentinelCircuitBreakerFactory matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.client.circuitbreaker.ReactiveCircuitBreakerFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   ReactiveSentinelCircuitBreakerAutoConfiguration.ReactiveSentinelCustomizerConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'reactor.core.publisher.Mono', 'reactor.core.publisher.Flux' (OnClassCondition)\r\n\r\n   RedisAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)\r\n\r\n   RedisAutoConfiguration#stringRedisTemplate matched:\r\n      - @ConditionalOnSingleCandidate (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found a primary bean from beans 'redisConnectionFactory'; @ConditionalOnMissingBean (types: org.springframework.data.redis.core.StringRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   RedisCacheConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)\r\n      - Cache org.springframework.boot.autoconfigure.cache.RedisCacheConfiguration automatic cache type (CacheCondition)\r\n      - @ConditionalOnBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found bean 'redisConnectionFactory'; @ConditionalOnMissingBean (types: org.springframework.cache.CacheManager; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   RedisReactiveAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'org.springframework.data.redis.connection.ReactiveRedisConnectionFactory', 'org.springframework.data.redis.core.ReactiveRedisTemplate', 'reactor.core.publisher.Flux' (OnClassCondition)\r\n\r\n   RedisReactiveAutoConfiguration#reactiveRedisTemplate matched:\r\n      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redisConnectionFactory'; @ConditionalOnMissingBean (names: reactiveRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   RedisReactiveAutoConfiguration#reactiveStringRedisTemplate matched:\r\n      - @ConditionalOnBean (types: org.springframework.data.redis.connection.ReactiveRedisConnectionFactory; SearchStrategy: all) found bean 'redisConnectionFactory'; @ConditionalOnMissingBean (names: reactiveStringRedisTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   RedisRepositoriesAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)\r\n      - @ConditionalOnProperty (spring.data.redis.repositories.enabled=true) matched (OnPropertyCondition)\r\n      - @ConditionalOnBean (types: org.springframework.data.redis.connection.RedisConnectionFactory; SearchStrategy: all) found bean 'redisConnectionFactory'; @ConditionalOnMissingBean (types: org.springframework.data.redis.repository.support.RedisRepositoryFactoryBean; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   RefreshAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.cloud.context.scope.refresh.RefreshScope' (OnClassCondition)\r\n      - @ConditionalOnProperty (spring.cloud.refresh.enabled) matched (OnPropertyCondition)\r\n\r\n   RefreshAutoConfiguration#legacyContextRefresher matched:\r\n      - AnyNestedCondition 1 matched 2 did not; NestedCondition on ConditionalOnBootstrapEnabled.OnBootstrapEnabledCondition.OnBootstrapEnabled @ConditionalOnProperty (spring.cloud.bootstrap.enabled) did not find property 'spring.cloud.bootstrap.enabled'; NestedCondition on ConditionalOnBootstrapEnabled.OnBootstrapEnabledCondition.OnUseLegacyProcessingEnabled @ConditionalOnProperty (spring.config.use-legacy-processing) did not find property 'spring.config.use-legacy-processing'; NestedCondition on ConditionalOnBootstrapEnabled.OnBootstrapEnabledCondition.OnBootstrapMarkerClassPresent @ConditionalOnClass found required class 'org.springframework.cloud.bootstrap.marker.Marker' (ConditionalOnBootstrapEnabled.OnBootstrapEnabledCondition)\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.context.refresh.LegacyContextRefresher; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   RefreshAutoConfiguration#loggingRebinder matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.logging.LoggingRebinder; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   RefreshAutoConfiguration#refreshScope matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.context.scope.refresh.RefreshScope; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   RestTemplateAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)\r\n      - NoneNestedConditions 0 matched 1 did not; NestedCondition on RestTemplateAutoConfiguration.NotReactiveWebApplicationCondition.ReactiveWebApplication did not find reactive web application classes (RestTemplateAutoConfiguration.NotReactiveWebApplicationCondition)\r\n\r\n   RestTemplateAutoConfiguration#restTemplateBuilder matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   RestTemplateAutoConfiguration#restTemplateBuilderConfigurer matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.client.RestTemplateBuilderConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   SaRedis_MysqlServiceImpl matched:\r\n      - @ConditionalOnProperty (inks.redisType=mysql) matched (OnPropertyCondition)\r\n\r\n   SentinelAutoConfiguration matched:\r\n      - @ConditionalOnProperty (spring.cloud.sentinel.enabled) matched (OnPropertyCondition)\r\n\r\n   SentinelAutoConfiguration#sentinelBeanPostProcessor matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)\r\n      - @ConditionalOnProperty (resttemplate.sentinel.enabled=true) matched (OnPropertyCondition)\r\n      - @ConditionalOnMissingBean (types: com.alibaba.cloud.sentinel.custom.SentinelBeanPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   SentinelAutoConfiguration#sentinelDataSourceHandler matched:\r\n      - @ConditionalOnMissingBean (types: com.alibaba.cloud.sentinel.custom.SentinelDataSourceHandler; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   SentinelAutoConfiguration#sentinelResourceAspect matched:\r\n      - @ConditionalOnMissingBean (types: com.alibaba.csp.sentinel.annotation.aspectj.SentinelResourceAspect; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   SentinelAutoConfiguration.SentinelConverterConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)\r\n\r\n   SentinelAutoConfiguration.SentinelConverterConfiguration.SentinelXmlConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)\r\n\r\n   SentinelCircuitBreakerAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.alibaba.csp.sentinel.SphU' (OnClassCondition)\r\n      - @ConditionalOnProperty (spring.cloud.circuitbreaker.sentinel.enabled=true) matched (OnPropertyCondition)\r\n\r\n   SentinelCircuitBreakerAutoConfiguration#sentinelCircuitBreakerFactory matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.client.circuitbreaker.CircuitBreakerFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   SentinelFeignAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'com.alibaba.csp.sentinel.SphU', 'feign.Feign' (OnClassCondition)\r\n\r\n   SentinelWebAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'com.alibaba.csp.sentinel.adapter.spring.webmvc.SentinelWebInterceptor' (OnClassCondition)\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n      - @ConditionalOnProperty (spring.cloud.sentinel.enabled) matched (OnPropertyCondition)\r\n\r\n   SentinelWebAutoConfiguration#sentinelWebInterceptor matched:\r\n      - @ConditionalOnProperty (spring.cloud.sentinel.filter.enabled) matched (OnPropertyCondition)\r\n\r\n   SentinelWebAutoConfiguration#sentinelWebMvcConfig matched:\r\n      - @ConditionalOnProperty (spring.cloud.sentinel.filter.enabled) matched (OnPropertyCondition)\r\n\r\n   ServletWebServerFactoryAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'javax.servlet.ServletRequest' (OnClassCondition)\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n\r\n   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:\r\n      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)\r\n\r\n   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:\r\n      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)\r\n\r\n   SimpleDiscoveryClientAutoConfiguration#simpleDiscoveryProperties matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.client.discovery.simple.SimpleDiscoveryProperties; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   SpringApplicationAdminJmxAutoConfiguration matched:\r\n      - @ConditionalOnProperty (spring.application.admin.enabled=true) matched (OnPropertyCondition)\r\n\r\n   SpringApplicationAdminJmxAutoConfiguration#springApplicationAdminRegistrar matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.admin.SpringApplicationAdminMXBeanRegistrar; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   SpringDataWebAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'org.springframework.data.web.PageableHandlerMethodArgumentResolver', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n      - @ConditionalOnMissingBean (types: org.springframework.data.web.PageableHandlerMethodArgumentResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   SpringDataWebAutoConfiguration#pageableCustomizer matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.PageableHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   SpringDataWebAutoConfiguration#sortCustomizer matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.data.web.config.SortHandlerMethodArgumentResolverCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   SqlInitializationAutoConfiguration matched:\r\n      - @ConditionalOnProperty (spring.sql.init.enabled) matched (OnPropertyCondition)\r\n      - NoneNestedConditions 0 matched 1 did not; NestedCondition on SqlInitializationAutoConfiguration.SqlInitializationModeCondition.ModeIsNever @ConditionalOnProperty (spring.sql.init.mode=never) did not find property 'mode' (SqlInitializationAutoConfiguration.SqlInitializationModeCondition)\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.sql.init.AbstractScriptDatabaseInitializer; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   Swagger2DocumentationConfiguration matched:\r\n      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)\r\n\r\n   TaskExecutionAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)\r\n\r\n   TaskExecutionAutoConfiguration#applicationTaskExecutor matched:\r\n      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   TaskExecutionAutoConfiguration#taskExecutorBuilder matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   TaskSchedulingAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)\r\n\r\n   TaskSchedulingAutoConfiguration#taskSchedulerBuilder matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   ThymeleafAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'org.thymeleaf.templatemode.TemplateMode', 'org.thymeleaf.spring5.SpringTemplateEngine' (OnClassCondition)\r\n\r\n   ThymeleafAutoConfiguration.DefaultTemplateResolverConfiguration matched:\r\n      - @ConditionalOnMissingBean (names: defaultTemplateResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   ThymeleafAutoConfiguration.ThymeleafDefaultConfiguration#templateEngine matched:\r\n      - @ConditionalOnMissingBean (types: org.thymeleaf.spring5.ISpringTemplateEngine; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect matched:\r\n      - @ConditionalOnClass found required class 'org.thymeleaf.extras.java8time.dialect.Java8TimeDialect' (OnClassCondition)\r\n\r\n   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect#java8TimeDialect matched:\r\n      - @ConditionalOnMissingBean (types: org.thymeleaf.extras.java8time.dialect.Java8TimeDialect; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration matched:\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n      - @ConditionalOnProperty (spring.thymeleaf.enabled) matched (OnPropertyCondition)\r\n\r\n   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration.ThymeleafViewResolverConfiguration#thymeleafViewResolver matched:\r\n      - @ConditionalOnMissingBean (names: thymeleafViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   TransactionAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)\r\n\r\n   TransactionAutoConfiguration#platformTransactionManagerCustomizers matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   TransactionAutoConfiguration.EnableTransactionManagementConfiguration matched:\r\n      - @ConditionalOnBean (types: org.springframework.transaction.TransactionManager; SearchStrategy: all) found bean 'transactionManager'; @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration matched:\r\n      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)\r\n\r\n   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:\r\n      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a primary bean from beans 'transactionManager' (OnBeanCondition)\r\n\r\n   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   UtilAutoConfiguration matched:\r\n      - @ConditionalOnProperty (spring.cloud.util.enabled) matched (OnPropertyCondition)\r\n\r\n   UtilAutoConfiguration#inetUtils matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.cloud.commons.util.InetUtils; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   ValidationAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'javax.validation.executable.ExecutableValidator' (OnClassCondition)\r\n      - @ConditionalOnResource found location classpath:META-INF/services/javax.validation.spi.ValidationProvider (OnResourceCondition)\r\n\r\n   ValidationAutoConfiguration#defaultValidator matched:\r\n      - @ConditionalOnMissingBean (types: javax.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   ValidationAutoConfiguration#methodValidationPostProcessor matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   WebMvcAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   WebMvcAutoConfiguration#formContentFilter matched:\r\n      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)\r\n      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   WebMvcAutoConfiguration.EnableWebMvcConfiguration#flashMapManager matched:\r\n      - @ConditionalOnMissingBean (names: flashMapManager; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   WebMvcAutoConfiguration.EnableWebMvcConfiguration#localeResolver matched:\r\n      - @ConditionalOnMissingBean (names: localeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   WebMvcAutoConfiguration.EnableWebMvcConfiguration#themeResolver matched:\r\n      - @ConditionalOnMissingBean (names: themeResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:\r\n      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:\r\n      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   WebSocketMessagingAutoConfiguration matched:\r\n      - @ConditionalOnClass found required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n\r\n   WebSocketServletAutoConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'javax.websocket.server.ServerContainer' (OnClassCondition)\r\n      - found 'session' scope (OnWebApplicationCondition)\r\n\r\n   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:\r\n      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)\r\n\r\n   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:\r\n      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n\r\nNegative matches:\r\n-----------------\r\n\r\n   ActiveMQAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)\r\n\r\n   AopAutoConfiguration.AspectJAutoProxyingConfiguration.JdkDynamicAutoProxyConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)\r\n\r\n   AopAutoConfiguration.ClassProxyingConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnMissingClass found unwanted class 'org.aspectj.weaver.Advice' (OnClassCondition)\r\n\r\n   ArtemisAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)\r\n\r\n   AsyncLoadBalancerAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnBean (types: org.springframework.cloud.client.loadbalancer.LoadBalancerClient; SearchStrategy: all) did not find any beans of type org.springframework.cloud.client.loadbalancer.LoadBalancerClient (OnBeanCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required class 'org.springframework.web.client.AsyncRestTemplate' (OnClassCondition)\r\n\r\n   BatchAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)\r\n\r\n   CacheAutoConfiguration.CacheManagerEntityManagerFactoryDependsOnPostProcessor:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)\r\n\r\n   CaffeineCacheConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.github.benmanes.caffeine.cache.Caffeine' (OnClassCondition)\r\n\r\n   CassandraAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)\r\n\r\n   CassandraDataAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)\r\n\r\n   CassandraReactiveDataAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)\r\n\r\n   CassandraReactiveRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)\r\n\r\n   CassandraRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.datastax.oss.driver.api.core.CqlSession' (OnClassCondition)\r\n\r\n   ClientHttpConnectorAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)\r\n\r\n   CloudHypermediaAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.Link' (OnClassCondition)\r\n\r\n   CodecsAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)\r\n\r\n   CommonsClientAutoConfiguration.ActuatorConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.boot.actuate.endpoint.annotation.Endpoint' (OnClassCondition)\r\n\r\n   CommonsClientAutoConfiguration.DiscoveryLoadBalancerConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.boot.actuate.health.HealthIndicator' (OnClassCondition)\r\n\r\n   CouchbaseAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)\r\n\r\n   CouchbaseCacheConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)\r\n\r\n   CouchbaseDataAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)\r\n\r\n   CouchbaseReactiveDataAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)\r\n\r\n   CouchbaseReactiveRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)\r\n\r\n   CouchbaseRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)\r\n\r\n   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:\r\n      Did not match:\r\n         - EmbeddedDataSource spring.datasource.url is set (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)\r\n\r\n   DataSourceConfiguration.Dbcp2:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)\r\n\r\n   DataSourceConfiguration.Generic:\r\n      Did not match:\r\n         - @ConditionalOnProperty (spring.datasource.type) did not find property 'spring.datasource.type' (OnPropertyCondition)\r\n\r\n   DataSourceConfiguration.OracleUcp:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSourceImpl', 'oracle.jdbc.OracleConnection' (OnClassCondition)\r\n\r\n   DataSourceConfiguration.Tomcat:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)\r\n\r\n   DataSourceInitializationConfiguration.InitializationSpecificCredentialsDataSourceInitializationConfiguration:\r\n      Did not match:\r\n         - AnyNestedCondition 0 matched 2 did not; NestedCondition on DataSourceInitializationConfiguration.InitializationSpecificCredentialsDataSourceInitializationConfiguration.DifferentCredentialsCondition.DataCredentials @ConditionalOnProperty (spring.datasource.data-username) did not find property 'data-username'; NestedCondition on DataSourceInitializationConfiguration.InitializationSpecificCredentialsDataSourceInitializationConfiguration.DifferentCredentialsCondition.SchemaCredentials @ConditionalOnProperty (spring.datasource.schema-username) did not find property 'schema-username' (DataSourceInitializationConfiguration.InitializationSpecificCredentialsDataSourceInitializationConfiguration.DifferentCredentialsCondition)\r\n\r\n   DataSourceInitializationConfiguration.SharedCredentialsDataSourceInitializationConfiguration:\r\n      Did not match:\r\n         - DataSource Initialization did not find configured properties spring.datasource.data-username, spring.datasource.schema-password, spring.datasource.schema[0], spring.datasource.data[0], spring.datasource.data, spring.datasource.schema-username, spring.datasource.data-password, spring.datasource.initialization-mode, spring.datasource.schema, spring.datasource.sql-script-encoding, spring.datasource.separator, spring.datasource.platform, spring.datasource.continue-on-error (DataSourceInitializationConfiguration.SharedCredentialsDataSourceInitializationConfiguration.DataSourceInitializationCondition)\r\n\r\n   DataSourceJmxConfiguration.TomcatDataSourceJmxConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSourceProxy' (OnClassCondition)\r\n\r\n   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)\r\n\r\n   DataSourcePoolMetadataProvidersConfiguration.OracleUcpPoolDataSourceMetadataProviderConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'oracle.ucp.jdbc.PoolDataSource', 'oracle.jdbc.OracleConnection' (OnClassCondition)\r\n\r\n   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)\r\n\r\n   DefaultGzipDecoderConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnProperty (feign.compression.response.enabled) did not find property 'feign.compression.response.enabled' (OnPropertyCondition)\r\n\r\n   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:\r\n      Did not match:\r\n         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)\r\n\r\n   EasyPoiAutoConfiguration#beanNameViewResolver:\r\n      Did not match:\r\n         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)\r\n\r\n   EhCacheCacheConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'net.sf.ehcache.Cache' (OnClassCondition)\r\n\r\n   ElasticsearchDataAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate' (OnClassCondition)\r\n\r\n   ElasticsearchRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)\r\n\r\n   ElasticsearchRestClientAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestHighLevelClient' (OnClassCondition)\r\n\r\n   EmbeddedLdapAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)\r\n\r\n   EmbeddedMongoAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClientSettings' (OnClassCondition)\r\n\r\n   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)\r\n\r\n   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)\r\n\r\n   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)\r\n\r\n   ErrorWebFluxAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)\r\n\r\n   FeignAcceptGzipEncodingAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnProperty (feign.compression.response.enabled) did not find property 'feign.compression.response.enabled' (OnPropertyCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required class 'feign.Feign' (OnClassCondition)\r\n\r\n   FeignAutoConfiguration.CircuitBreakerPresentFeignTargeterConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnProperty (feign.circuitbreaker.enabled=true) did not find property 'feign.circuitbreaker.enabled' (OnPropertyCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required class 'org.springframework.cloud.client.circuitbreaker.CircuitBreaker' (OnClassCondition)\r\n\r\n   FeignAutoConfiguration.FeignJacksonConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnProperty (feign.autoconfiguration.jackson.enabled=true) did not find property 'feign.autoconfiguration.jackson.enabled' (OnPropertyCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required classes 'com.fasterxml.jackson.databind.Module', 'org.springframework.data.domain.Page', 'org.springframework.data.domain.Sort' (OnClassCondition)\r\n\r\n   FeignAutoConfiguration.HttpClient5FeignConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'feign.hc5.ApacheHttp5Client' (OnClassCondition)\r\n\r\n   FeignAutoConfiguration.HttpClientFeignConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'feign.httpclient.ApacheHttpClient' (OnClassCondition)\r\n\r\n   FeignAutoConfiguration.Oauth2FeignConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.client.OAuth2ClientContext' (OnClassCondition)\r\n\r\n   FeignAutoConfiguration.OkHttpFeignConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'feign.okhttp.OkHttpClient' (OnClassCondition)\r\n\r\n   FeignConfig:\r\n      Did not match:\r\n         - @ConditionalOnProperty (inks.svcfeign) did not find property 'inks.svcfeign' (OnPropertyCondition)\r\n\r\n   FeignContentGzipEncodingAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnProperty (feign.compression.request.enabled) did not find property 'feign.compression.request.enabled' (OnPropertyCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required class 'feign.Feign' (OnClassCondition)\r\n\r\n   FeignHalAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.RepresentationModel' (OnClassCondition)\r\n\r\n   FeignLoadBalancerAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnBean did not find required type 'org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory' (OnBeanCondition)\r\n\r\n   FlywayAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnProperty (spring.flyway.enabled) found different value in property 'enabled' (OnPropertyCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required class 'org.flywaydb.core.Flyway' (OnClassCondition)\r\n\r\n   FreeMarkerAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)\r\n\r\n   GenericCacheConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnBean (types: org.springframework.cache.Cache; SearchStrategy: all) did not find any beans of type org.springframework.cache.Cache (OnBeanCondition)\r\n      Matched:\r\n         - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration automatic cache type (CacheCondition)\r\n\r\n   GroovyTemplateAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)\r\n\r\n   GsonHttpMessageConvertersConfiguration.GsonHttpMessageConverterConfiguration:\r\n      Did not match:\r\n         - AnyNestedCondition 0 matched 2 did not; NestedCondition on GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition.JacksonJsonbUnavailable NoneNestedConditions 1 matched 1 did not; NestedCondition on GsonHttpMessageConvertersConfiguration.JacksonAndJsonbUnavailableCondition.JsonbPreferred @ConditionalOnProperty (spring.mvc.converters.preferred-json-mapper=jsonb) did not find property 'spring.mvc.converters.preferred-json-mapper'; NestedCondition on GsonHttpMessageConvertersConfiguration.JacksonAndJsonbUnavailableCondition.JacksonAvailable @ConditionalOnBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) found bean 'mappingJackson2HttpMessageConverter'; NestedCondition on GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition.GsonPreferred @ConditionalOnProperty (spring.mvc.converters.preferred-json-mapper=gson) did not find property 'spring.mvc.converters.preferred-json-mapper' (GsonHttpMessageConvertersConfiguration.PreferGsonOrJacksonAndJsonbUnavailableCondition)\r\n\r\n   H2ConsoleAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.h2.server.web.WebServlet' (OnClassCondition)\r\n\r\n   HazelcastAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)\r\n\r\n   HazelcastCacheConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)\r\n\r\n   HazelcastJpaDependencyAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)\r\n\r\n   HibernateJpaAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManager' (OnClassCondition)\r\n\r\n   HttpHandlerAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)\r\n\r\n   HypermediaAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.EntityModel' (OnClassCondition)\r\n\r\n   InfinispanCacheConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager' (OnClassCondition)\r\n\r\n   InfluxDbAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.influxdb.InfluxDB' (OnClassCondition)\r\n\r\n   InitTableController:\r\n      Did not match:\r\n         - @ConditionalOnProperty (spring.flyway.auto-create-db=true) did not find property 'auto-create-db' (OnPropertyCondition)\r\n\r\n   IntegrationAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)\r\n\r\n   JCacheCacheConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'javax.cache.Caching' (OnClassCondition)\r\n\r\n   JdbcRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration' (OnClassCondition)\r\n\r\n   JedisConnectionConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'org.apache.commons.pool2.impl.GenericObjectPool', 'redis.clients.jedis.Jedis' (OnClassCondition)\r\n\r\n   JerseyAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)\r\n\r\n   JmsAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'javax.jms.Message' (OnClassCondition)\r\n\r\n   JndiConnectionFactoryAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)\r\n\r\n   JndiDataSourceAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)\r\n\r\n   JooqAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)\r\n\r\n   JpaRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)\r\n\r\n   JsonbAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)\r\n\r\n   JsonbHttpMessageConvertersConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)\r\n\r\n   JtaAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'javax.transaction.Transaction' (OnClassCondition)\r\n\r\n   KafkaAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)\r\n\r\n   LdapAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)\r\n\r\n   LdapRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)\r\n\r\n   LiquibaseAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)\r\n\r\n   LoadBalancerAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnBean (types: org.springframework.cloud.client.loadbalancer.LoadBalancerClient; SearchStrategy: all) did not find any beans of type org.springframework.cloud.client.loadbalancer.LoadBalancerClient (OnBeanCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)\r\n\r\n   LoadBalancerAutoConfiguration.RetryAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.retry.support.RetryTemplate' (OnClassCondition)\r\n         - Ancestor org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)\r\n\r\n   LoadBalancerAutoConfiguration.RetryInterceptorAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.retry.support.RetryTemplate' (OnClassCondition)\r\n         - Ancestor org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)\r\n\r\n   LoadBalancerBeanPostProcessorAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)\r\n\r\n   MailSenderAutoConfiguration:\r\n      Did not match:\r\n         - AnyNestedCondition 0 matched 2 did not; NestedCondition on MailSenderAutoConfiguration.MailSenderCondition.JndiNameProperty @ConditionalOnProperty (spring.mail.jndi-name) did not find property 'jndi-name'; NestedCondition on MailSenderAutoConfiguration.MailSenderCondition.HostProperty @ConditionalOnProperty (spring.mail.host) did not find property 'host' (MailSenderAutoConfiguration.MailSenderCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required classes 'javax.mail.internet.MimeMessage', 'javax.activation.MimeType', 'org.springframework.mail.MailSender' (OnClassCondition)\r\n\r\n   MailSenderValidatorAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnProperty (spring.mail.test-connection) did not find property 'test-connection' (OnPropertyCondition)\r\n\r\n   MessageSourceAutoConfiguration:\r\n      Did not match:\r\n         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)\r\n\r\n   MongoAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)\r\n\r\n   MongoDataAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)\r\n\r\n   MongoReactiveAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)\r\n\r\n   MongoReactiveDataAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)\r\n\r\n   MongoReactiveRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)\r\n\r\n   MongoRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)\r\n\r\n   MustacheAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)\r\n\r\n   MybatisAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnMissingBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) found beans of type 'org.mybatis.spring.mapper.MapperScannerConfigurer' inks.sa.common.core.InksServiceSaCoreApplication#MapperScannerRegistrar#0, inks.service.sa.crm.InksServiceSaCrmApplication#MapperScannerRegistrar#0 (OnBeanCondition)\r\n\r\n   MybatisLanguageDriverAutoConfiguration.FreeMarkerConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)\r\n\r\n   MybatisLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)\r\n\r\n   MybatisLanguageDriverAutoConfiguration.LegacyVelocityConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.velocity.Driver' (OnClassCondition)\r\n\r\n   MybatisLanguageDriverAutoConfiguration.ThymeleafConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)\r\n\r\n   MybatisLanguageDriverAutoConfiguration.VelocityConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)\r\n\r\n   Neo4jAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)\r\n\r\n   Neo4jDataAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)\r\n\r\n   Neo4jReactiveDataAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)\r\n\r\n   Neo4jReactiveRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)\r\n\r\n   Neo4jRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.neo4j.driver.Driver' (OnClassCondition)\r\n\r\n   NoOpCacheConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnMissingBean (types: org.springframework.cache.CacheManager; SearchStrategy: all) found beans of type 'org.springframework.cache.CacheManager' cacheManager (OnBeanCondition)\r\n      Matched:\r\n         - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration automatic cache type (CacheCondition)\r\n\r\n   OAuth2ClientAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)\r\n\r\n   OAuth2ResourceServerAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.server.resource.BearerTokenAuthenticationToken' (OnClassCondition)\r\n\r\n   ProjectInfoAutoConfiguration#buildProperties:\r\n      Did not match:\r\n         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)\r\n\r\n   ProjectInfoAutoConfiguration#gitProperties:\r\n      Did not match:\r\n         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)\r\n\r\n   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer:\r\n      Did not match:\r\n         - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) found beans of type 'org.springframework.context.support.PropertySourcesPlaceholderConfigurer' placeholderConfigurer (OnBeanCondition)\r\n\r\n   QuartzAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)\r\n\r\n   R2dbcAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)\r\n\r\n   R2dbcDataAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.data.r2dbc.core.R2dbcEntityTemplate' (OnClassCondition)\r\n\r\n   R2dbcInitializationConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'io.r2dbc.spi.ConnectionFactory', 'org.springframework.r2dbc.connection.init.DatabasePopulator' (OnClassCondition)\r\n\r\n   R2dbcRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'io.r2dbc.spi.ConnectionFactory' (OnClassCondition)\r\n\r\n   R2dbcTransactionManagerAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.r2dbc.connection.R2dbcTransactionManager' (OnClassCondition)\r\n\r\n   RSocketMessagingAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)\r\n\r\n   RSocketRequesterAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)\r\n\r\n   RSocketSecurityAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor' (OnClassCondition)\r\n\r\n   RSocketServerAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'io.rsocket.core.RSocketServer' (OnClassCondition)\r\n\r\n   RSocketStrategiesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'io.rsocket.RSocket' (OnClassCondition)\r\n\r\n   RabbitAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)\r\n\r\n   ReactiveCommonsClientAutoConfiguration.ReactiveDiscoveryLoadBalancerConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'org.springframework.boot.actuate.health.ReactiveHealthIndicator', 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)\r\n\r\n   ReactiveCompositeDiscoveryClientAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)\r\n\r\n   ReactiveElasticsearchRepositoriesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.data.elasticsearch.client.reactive.ReactiveElasticsearchClient' (OnClassCondition)\r\n\r\n   ReactiveElasticsearchRestClientAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'reactor.netty.http.client.HttpClient' (OnClassCondition)\r\n\r\n   ReactiveOAuth2ClientAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)\r\n\r\n   ReactiveOAuth2ResourceServerAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)\r\n\r\n   ReactiveSecurityAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)\r\n\r\n   ReactiveUserDetailsServiceAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)\r\n\r\n   ReactiveWebServerFactoryAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)\r\n\r\n   ReactorLoadBalancerClientAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)\r\n\r\n   RedisAutoConfiguration#redisTemplate:\r\n      Did not match:\r\n         - @ConditionalOnMissingBean (names: redisTemplate; SearchStrategy: all) found beans named redisTemplate (OnBeanCondition)\r\n\r\n   RefreshAutoConfiguration#configDataContextRefresher:\r\n      Did not match:\r\n         - NoneNestedConditions 1 matched 2 did not; NestedCondition on ConditionalOnBootstrapDisabled.OnBootstrapDisabledCondition.OnBootstrapEnabled @ConditionalOnProperty (spring.cloud.bootstrap.enabled) did not find property 'spring.cloud.bootstrap.enabled'; NestedCondition on ConditionalOnBootstrapDisabled.OnBootstrapDisabledCondition.OnUseLegacyProcessingEnabled @ConditionalOnProperty (spring.config.use-legacy-processing) did not find property 'spring.config.use-legacy-processing'; NestedCondition on ConditionalOnBootstrapDisabled.OnBootstrapDisabledCondition.OnBootstrapMarkerClassPresent @ConditionalOnClass found required class 'org.springframework.cloud.bootstrap.marker.Marker' (ConditionalOnBootstrapDisabled.OnBootstrapDisabledCondition)\r\n      Matched:\r\n         - @ConditionalOnMissingBean (types: org.springframework.cloud.context.refresh.ConfigDataContextRefresher; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   RefreshAutoConfiguration.JpaInvokerConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManagerFactory' (OnClassCondition)\r\n\r\n   RefreshEndpointAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.boot.actuate.autoconfigure.endpoint.EndpointAutoConfiguration' (OnClassCondition)\r\n\r\n   RepositoryRestMvcAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)\r\n\r\n   ResourceServerTokenRelayAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfiguration' (OnClassCondition)\r\n\r\n   SaRedis_RedisServiceImpl:\r\n      Did not match:\r\n         - @ConditionalOnProperty (inks.redisType=redis) found different value in property 'inks.redisType' (OnPropertyCondition)\r\n\r\n   Saml2RelyingPartyAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository' (OnClassCondition)\r\n\r\n   SecurityAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)\r\n\r\n   SecurityFilterAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)\r\n\r\n   SendGridAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)\r\n\r\n   SentinelEndpointAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.boot.actuate.endpoint.annotation.Endpoint' (OnClassCondition)\r\n\r\n   SentinelFeignAutoConfiguration#feignSentinelBuilder:\r\n      Did not match:\r\n         - @ConditionalOnProperty (feign.sentinel.enabled) did not find property 'feign.sentinel.enabled' (OnPropertyCondition)\r\n\r\n   SentinelWebFluxAutoConfiguration:\r\n      Did not match:\r\n         - did not find reactive web application classes (OnWebApplicationCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required class 'com.alibaba.csp.sentinel.adapter.reactor.SentinelReactorTransformer' (OnClassCondition)\r\n\r\n   ServiceRegistryAutoConfiguration.ServiceRegistryEndpointConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.boot.actuate.endpoint.annotation.Endpoint' (OnClassCondition)\r\n\r\n   ServletWebServerFactoryAutoConfiguration#forwardedHeaderFilter:\r\n      Did not match:\r\n         - @ConditionalOnProperty (server.forward-headers-strategy=framework) did not find property 'server.forward-headers-strategy' (OnPropertyCondition)\r\n\r\n   ServletWebServerFactoryConfiguration.EmbeddedJetty:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)\r\n\r\n   ServletWebServerFactoryConfiguration.EmbeddedUndertow:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)\r\n\r\n   SessionAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)\r\n\r\n   SimpleCacheConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnMissingBean (types: org.springframework.cache.CacheManager; SearchStrategy: all) found beans of type 'org.springframework.cache.CacheManager' cacheManager (OnBeanCondition)\r\n      Matched:\r\n         - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration automatic cache type (CacheCondition)\r\n\r\n   SimpleReactiveDiscoveryClientAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)\r\n\r\n   SolrAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.impl.CloudSolrClient' (OnClassCondition)\r\n\r\n   TaskSchedulingAutoConfiguration#taskScheduler:\r\n      Did not match:\r\n         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)\r\n\r\n   ThymeleafAutoConfiguration.DataAttributeDialectConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'com.github.mxab.thymeleaf.extras.dataattribute.dialect.DataAttributeDialect' (OnClassCondition)\r\n\r\n   ThymeleafAutoConfiguration.ThymeleafReactiveConfiguration:\r\n      Did not match:\r\n         - did not find reactive web application classes (OnWebApplicationCondition)\r\n\r\n   ThymeleafAutoConfiguration.ThymeleafSecurityDialectConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.thymeleaf.extras.springsecurity5.dialect.SpringSecurityDialect' (OnClassCondition)\r\n\r\n   ThymeleafAutoConfiguration.ThymeleafWebFluxConfiguration:\r\n      Did not match:\r\n         - did not find reactive web application classes (OnWebApplicationCondition)\r\n\r\n   ThymeleafAutoConfiguration.ThymeleafWebLayoutConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'nz.net.ultraq.thymeleaf.LayoutDialect' (OnClassCondition)\r\n\r\n   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration#resourceUrlEncodingFilter:\r\n      Did not match:\r\n         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)\r\n\r\n   TransactionAutoConfiguration#transactionalOperator:\r\n      Did not match:\r\n         - @ConditionalOnSingleCandidate (types: org.springframework.transaction.ReactiveTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)\r\n\r\n   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)\r\n\r\n   UserDetailsServiceAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)\r\n\r\n   WebClientAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)\r\n\r\n   WebFluxAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)\r\n\r\n   WebMvcAutoConfiguration#hiddenHttpMethodFilter:\r\n      Did not match:\r\n         - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) did not find property 'enabled' (OnPropertyCondition)\r\n\r\n   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)\r\n\r\n   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:\r\n      Did not match:\r\n         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)\r\n\r\n   WebServiceTemplateAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.ws.client.core.WebServiceTemplate' (OnClassCondition)\r\n\r\n   WebServicesAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)\r\n\r\n   WebSocketMessagingAutoConfiguration.WebSocketMessageConverterConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnBean (types: org.springframework.web.socket.config.annotation.DelegatingWebSocketMessageBrokerConfiguration,com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans of type org.springframework.web.socket.config.annotation.DelegatingWebSocketMessageBrokerConfiguration (OnBeanCondition)\r\n      Matched:\r\n         - @ConditionalOnClass found required classes 'com.fasterxml.jackson.databind.ObjectMapper', 'org.springframework.messaging.simp.config.AbstractMessageBrokerConfiguration' (OnClassCondition)\r\n\r\n   WebSocketReactiveAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)\r\n\r\n   WebSocketServletAutoConfiguration.Jetty10WebSocketConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.websocket.javax.server.internal.JavaxWebSocketServerContainer', 'org.eclipse.jetty.websocket.server.JettyWebSocketServerContainer' (OnClassCondition)\r\n\r\n   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.websocket.jsr356.server.deploy.WebSocketServerContainerInitializer' (OnClassCondition)\r\n\r\n   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)\r\n\r\n   WritableEnvironmentEndpointAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'org.springframework.boot.actuate.autoconfigure.env.EnvironmentEndpointProperties' (OnClassCondition)\r\n\r\n   XADataSourceAutoConfiguration:\r\n      Did not match:\r\n         - @ConditionalOnClass did not find required class 'javax.transaction.TransactionManager' (OnClassCondition)\r\n\r\n\r\nExclusions:\r\n-----------\r\n\r\n    None\r\n\r\n\r\nUnconditional classes:\r\n----------------------\r\n\r\n    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration\r\n\r\n    org.springframework.cloud.client.ReactiveCommonsClientAutoConfiguration\r\n\r\n    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration\r\n\r\n    org.springframework.cloud.client.discovery.simple.SimpleDiscoveryClientAutoConfiguration\r\n\r\n    org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration\r\n\r\n    org.springframework.cloud.client.CommonsClientAutoConfiguration\r\n\r\n    org.springframework.cloud.commons.httpclient.HttpClientConfiguration\r\n\r\n    org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration\r\n\r\n    org.springframework.cloud.client.serviceregistry.ServiceRegistryAutoConfiguration\r\n\r\n    org.springframework.cloud.autoconfigure.LifecycleMvcEndpointAutoConfiguration\r\n\r\n    cn.hutool.extra.spring.SpringUtil\r\n\r\n    org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration\r\n\r\n    com.cool.request.components.ComponentLoader\r\n\r\n    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration\r\n\r\n\r\n","logger_name":"org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:59.271+08:00","@version":"1","message":"Found key 'spring.liveBeansView.mbeanDomain' in PropertySource 'systemProperties' with value of type String","logger_name":"org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertyResolver$DefaultResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:59.272+08:00","@version":"1","message":"Started InksServiceSaCrmApplication in 25.295 seconds (JVM running for 28.521)","logger_name":"inks.service.sa.crm.InksServiceSaCrmApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:59.273+08:00","@version":"1","message":"Application availability state LivenessState changed to CORRECT","logger_name":"org.springframework.boot.availability.ApplicationAvailabilityBean","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:59.276+08:00","@version":"1","message":"Application availability state ReadinessState changed to ACCEPTING_TRAFFIC","logger_name":"org.springframework.boot.availability.ApplicationAvailabilityBean","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:10:59.278+08:00","@version":"1","message":"This is an INFO logMon Jul 28 13:10:59 CST 2025","logger_name":"inks.service.sa.crm.InksServiceSaCrmApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:10:59.278+08:00","@version":"1","message":"This is a WARN logMon Jul 28 13:10:59 CST 2025","logger_name":"inks.service.sa.crm.InksServiceSaCrmApplication","thread_name":"main","level":"WARN","level_value":30000}
{"@timestamp":"2025-07-28T13:10:59.278+08:00","@version":"1","message":"This is an ERROR logMon Jul 28 13:10:59 CST 2025","logger_name":"inks.service.sa.crm.InksServiceSaCrmApplication","thread_name":"main","level":"ERROR","level_value":40000}
{"@timestamp":"2025-07-28T13:12:29.443+08:00","@version":"1","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"http-nio-10657-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:12:29.444+08:00","@version":"1","message":"Initializing Servlet 'dispatcherServlet'","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:12:29.445+08:00","@version":"1","message":"Detected StandardServletMultipartResolver","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.445+08:00","@version":"1","message":"Detected AcceptHeaderLocaleResolver","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.445+08:00","@version":"1","message":"Detected FixedThemeResolver","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.448+08:00","@version":"1","message":"Detected org.springframework.web.servlet.view.DefaultRequestToViewNameTranslator@2fd4a8da","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.448+08:00","@version":"1","message":"Detected org.springframework.web.servlet.support.SessionFlashMapManager@3cb03623","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.448+08:00","@version":"1","message":"enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.448+08:00","@version":"1","message":"Completed initialization in 4 ms","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-28T13:12:29.478+08:00","@version":"1","message":"GET \"/swagger-ui.html\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.493+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.714+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.841+08:00","@version":"1","message":"GET \"/webjars/springfox-swagger-ui/swagger-ui.css?v=2.9.2\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.841+08:00","@version":"1","message":"GET \"/webjars/springfox-swagger-ui/springfox.js?v=2.9.2\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.841+08:00","@version":"1","message":"GET \"/webjars/springfox-swagger-ui/springfox.css?v=2.9.2\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.841+08:00","@version":"1","message":"GET \"/webjars/springfox-swagger-ui/swagger-ui-bundle.js?v=2.9.2\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.842+08:00","@version":"1","message":"GET \"/webjars/springfox-swagger-ui/swagger-ui-standalone-preset.js?v=2.9.2\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.846+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/webjars/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.846+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/webjars/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.846+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/webjars/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.846+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/webjars/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.846+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/webjars/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.852+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.853+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.854+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.857+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:29.874+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.233+08:00","@version":"1","message":"GET \"/swagger-resources/configuration/ui\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.236+08:00","@version":"1","message":"Mapped to springfox.documentation.swagger.web.ApiResourceController#uiConfiguration()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.317+08:00","@version":"1","message":"Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.317+08:00","@version":"1","message":"Writing [springfox.documentation.swagger.web.UiConfiguration@5077fd62]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.327+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.335+08:00","@version":"1","message":"GET \"/swagger-resources/configuration/security\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.336+08:00","@version":"1","message":"Mapped to springfox.documentation.swagger.web.ApiResourceController#securityConfiguration()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.349+08:00","@version":"1","message":"Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.350+08:00","@version":"1","message":"Writing [springfox.documentation.swagger.web.SecurityConfiguration@3d1980f4]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.352+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.365+08:00","@version":"1","message":"GET \"/swagger-resources\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.366+08:00","@version":"1","message":"Mapped to springfox.documentation.swagger.web.ApiResourceController#swaggerResources()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.374+08:00","@version":"1","message":"Using 'application/json', given [application/json] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.374+08:00","@version":"1","message":"Writing [[springfox.documentation.swagger.web.SwaggerResource@8fa380]]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.382+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.584+08:00","@version":"1","message":"GET \"/v2/api-docs\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.589+08:00","@version":"1","message":"GET \"/\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.594+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/], Classpath [resources/], Classpath [static/], Classpath [public/], ServletContext [/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.598+08:00","@version":"1","message":"Resource not found","logger_name":"org.springframework.web.servlet.resource.ResourceHttpRequestHandler","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.598+08:00","@version":"1","message":"Completed 404 NOT_FOUND","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.602+08:00","@version":"1","message":"\"ERROR\" dispatch for GET \"/error\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.605+08:00","@version":"1","message":"Mapped to org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#error(HttpServletRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.611+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.612+08:00","@version":"1","message":"Writing [{timestamp=Mon Jul 28 13:12:32 CST 2025, status=404, error=Not Found, path=/}]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.619+08:00","@version":"1","message":"Exiting from \"ERROR\" dispatch, status 404","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.627+08:00","@version":"1","message":"GET \"/webjars/springfox-swagger-ui/fonts/titillium-web-v6-latin-700.woff2\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.627+08:00","@version":"1","message":"GET \"/webjars/springfox-swagger-ui/fonts/open-sans-v15-latin-regular.woff2\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.629+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/webjars/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.629+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/webjars/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.633+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.634+08:00","@version":"1","message":"GET \"/csrf\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.635+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.636+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/], Classpath [resources/], Classpath [static/], Classpath [public/], ServletContext [/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.639+08:00","@version":"1","message":"Resource not found","logger_name":"org.springframework.web.servlet.resource.ResourceHttpRequestHandler","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.639+08:00","@version":"1","message":"Completed 404 NOT_FOUND","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.639+08:00","@version":"1","message":"\"ERROR\" dispatch for GET \"/error\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.640+08:00","@version":"1","message":"Mapped to org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#error(HttpServletRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.641+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.641+08:00","@version":"1","message":"Writing [{timestamp=Mon Jul 28 13:12:32 CST 2025, status=404, error=Not Found, path=/csrf}]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.642+08:00","@version":"1","message":"Exiting from \"ERROR\" dispatch, status 404","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.864+08:00","@version":"1","message":"Using 'application/json', given [application/json, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.865+08:00","@version":"1","message":"Writing [springfox.documentation.spring.web.json.Json@3b5ad97e]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:32.874+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:33.284+08:00","@version":"1","message":"GET \"/webjars/springfox-swagger-ui/fonts/open-sans-v15-latin-700.woff2\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:33.284+08:00","@version":"1","message":"GET \"/webjars/springfox-swagger-ui/fonts/source-code-pro-v7-latin-300.woff2\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:33.288+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/webjars/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:33.288+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/webjars/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:33.292+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:33.294+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:34.637+08:00","@version":"1","message":"GET \"/\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:34.640+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/], Classpath [resources/], Classpath [static/], Classpath [public/], ServletContext [/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:34.640+08:00","@version":"1","message":"Resource not found","logger_name":"org.springframework.web.servlet.resource.ResourceHttpRequestHandler","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:34.640+08:00","@version":"1","message":"Completed 404 NOT_FOUND","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:34.640+08:00","@version":"1","message":"\"ERROR\" dispatch for GET \"/error\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:34.641+08:00","@version":"1","message":"Mapped to org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#errorHtml(HttpServletRequest, HttpServletResponse)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:34.670+08:00","@version":"1","message":"Selected 'text/html' given [text/html, text/html;q=0.8]","logger_name":"org.springframework.web.servlet.view.ContentNegotiatingViewResolver","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:34.680+08:00","@version":"1","message":"Exiting from \"ERROR\" dispatch, status 404","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:35.393+08:00","@version":"1","message":"GET \"/favicon.ico\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:35.397+08:00","@version":"1","message":"Mapped to ResourceHttpRequestHandler [Classpath [META-INF/resources/], Classpath [resources/], Classpath [static/], Classpath [public/], ServletContext [/]]","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:35.402+08:00","@version":"1","message":"Resource not found","logger_name":"org.springframework.web.servlet.resource.ResourceHttpRequestHandler","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:35.402+08:00","@version":"1","message":"Completed 404 NOT_FOUND","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:35.402+08:00","@version":"1","message":"\"ERROR\" dispatch for GET \"/error\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:35.403+08:00","@version":"1","message":"Mapped to org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#error(HttpServletRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:35.404+08:00","@version":"1","message":"Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:35.404+08:00","@version":"1","message":"Writing [{timestamp=Mon Jul 28 13:12:35 CST 2025, status=404, error=Not Found, path=/favicon.ico}]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.HttpEntityMethodProcessor","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:12:35.415+08:00","@version":"1","message":"Exiting from \"ERROR\" dispatch, status 404","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.202+08:00","@version":"1","message":"POST \"/SaUser/login\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.202+08:00","@version":"1","message":"Mapped to inks.sa.common.core.controller.A_SaUserController#login(String, HttpServletRequest, String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.207+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"UserName\":\"admin\",\"Password\":\"123456\"}\"]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.598+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.598+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@259c397c]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.607+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.638+08:00","@version":"1","message":"GET \"/SaMenuApp/getMenuAppListByLoginUser?tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.639+08:00","@version":"1","message":"Mapped to inks.sa.common.core.controller.A_SaMenuappController#getMenuAppListByLoginUser()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.658+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.658+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@5fd5d7aa]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:55.660+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.070+08:00","@version":"1","message":"POST \"/S07M10B1/getOnlinePageList?own=1&tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.071+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M10B1Controller#getOnlinePageList(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.074+08:00","@version":"1","message":"POST \"/S07MBIR1/getNewCountThisMonth?own=1&tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.074+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07MBIR1Controller#getNewCountThisMonth(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.076+08:00","@version":"1","message":"POST \"/S07MBIR1/getAllCount?own=1&tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.076+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"OrderType\":1,\"PageNum\":1,\"PageSize\":8,\"SearchType\":1,\"scenedata\":[{\"field\":\"Sa_Followview.IsAuto\" (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.076+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07MBIR1Controller#getAllCount(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.076+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"DateRange\":{\"StartDate\":\"2025-7-01 00:00:00\",\"EndDate\":\"2025-7-31 23:59:59\"}}\"]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.079+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{}\"]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.215+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.215+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@7c83e598]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.226+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.226+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@6753116c]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.227+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.227+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@3f77c858]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.227+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.227+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:22:57.231+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.331+08:00","@version":"1","message":"POST \"/S07M05B1/getPageTh?tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.331+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M05B1Controller#getPageTh(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.333+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":8,\"OrderType\":1,\"SearchType\":1,\"scenedata\":[]}\"]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.400+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.400+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@6abaf723]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.404+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.432+08:00","@version":"1","message":"POST \"/S07M17B1/getPageTh?tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.433+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M17B1Controller#getPageTh(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.437+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":8,\"OrderType\":1,\"SearchType\":1,\"scenedata\":[{\"field\":\"Sa_SmpPlan.businessid (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.438+08:00","@version":"1","message":"POST \"/S07M17B1/getPageTh?tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.438+08:00","@version":"1","message":"POST \"/S07M17B1/getPageTh?tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.438+08:00","@version":"1","message":"POST \"/S07M17B1/getPageTh?tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.438+08:00","@version":"1","message":"POST \"/S07M17B1/getPageTh?tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.438+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M17B1Controller#getPageTh(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.438+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M17B1Controller#getPageTh(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.438+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M17B1Controller#getPageTh(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.438+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M17B1Controller#getPageTh(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.439+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":8,\"OrderType\":1,\"SearchType\":1,\"scenedata\":[{\"field\":\"Sa_SmpPlan.businessid (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.439+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":8,\"OrderType\":1,\"SearchType\":1,\"scenedata\":[{\"field\":\"Sa_SmpPlan.businessid (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.439+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":8,\"OrderType\":1,\"SearchType\":1,\"scenedata\":[{\"field\":\"Sa_SmpPlan.businessid (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.439+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":8,\"OrderType\":1,\"SearchType\":1,\"scenedata\":[{\"field\":\"Sa_SmpPlan.businessid (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.440+08:00","@version":"1","message":"POST \"/S07M17B1/getPageTh?tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.440+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M17B1Controller#getPageTh(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.440+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":8,\"OrderType\":1,\"SearchType\":1,\"scenedata\":[{\"field\":\"Sa_SmpPlan.businessid (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.572+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.572+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@7a3abbd6]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.572+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.573+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@6af7bda5]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.573+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.573+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.574+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.574+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@6a6637fc]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.575+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.575+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.575+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@4012e089]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.577+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.577+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.577+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@49ad98c2]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.578+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.582+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.582+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@2f92c5a8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.584+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.762+08:00","@version":"1","message":"POST \"/S07M17B1/getPageTh?tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.762+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M17B1Controller#getPageTh(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.762+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":8,\"OrderType\":1,\"SearchType\":1,\"scenedata\":[{\"field\":\"Sa_SmpPlan.businessid (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.763+08:00","@version":"1","message":"POST \"/S07M17B1/getPageTh?tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.763+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M17B1Controller#getPageTh(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.763+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":8,\"OrderType\":1,\"SearchType\":1,\"scenedata\":[{\"field\":\"Sa_SmpPlan.businessid (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.810+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.810+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.810+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@6401bea0]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.810+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@12cf0d52]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.811+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:05.811+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-9","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:06.590+08:00","@version":"1","message":"GET \"/S07M05B1/getBillEntity?key=1949700483513843712&tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:06.590+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M05B1Controller#getBillEntity(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:06.611+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:06.611+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@4265988f]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:06.613+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:09.290+08:00","@version":"1","message":"GET \"/OA/getListByModuleCode?code=S07M05B1Edit&tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:09.290+08:00","@version":"1","message":"Mapped to inks.sa.common.core.config.oa.OAController#getListByModuleCode(String, String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:09.469+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:09.469+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@35e376d6]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:23:09.473+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:24:40.625+08:00","@version":"1","message":"GET \"/SaUser/checkHard\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:24:40.626+08:00","@version":"1","message":"Mapped to inks.sa.common.core.controller.A_SaUserController#checkHard()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:24:40.653+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:24:40.654+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@4fb7880c]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:24:40.655+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-10","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:24:56.040+08:00","@version":"1","message":"GET \"/S07M05B1/getBillEntity?key=1949700483513843712&tid=tid-inks-crm\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:24:56.040+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M05B1Controller#getBillEntity(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:24:56.055+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:24:56.055+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@53f82503]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:24:56.056+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:12.479+08:00","@version":"1","message":"GET \"/SaUser/checkHard\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:12.479+08:00","@version":"1","message":"Mapped to inks.sa.common.core.controller.A_SaUserController#checkHard()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:12.499+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:12.500+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@e9c742e]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:12.501+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.486+08:00","@version":"1","message":"POST \"/SaUser/login?type=1\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.486+08:00","@version":"1","message":"Mapped to inks.sa.common.core.controller.A_SaUserController#login(String, HttpServletRequest, String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.486+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"username\":\"admin\",\"password\":\"123456\"}\"]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.662+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.662+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@5ea264a]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.663+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.690+08:00","@version":"1","message":"GET \"/SaUser/getUserInfo\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.690+08:00","@version":"1","message":"Mapped to inks.sa.common.core.controller.A_SaUserController#getUserInfo()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.701+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.701+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@47de9a8d]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:16.703+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.711+08:00","@version":"1","message":"POST \"/S07M10B1/getPageList\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.711+08:00","@version":"1","message":"POST \"/S07MBIR1/getNewCountAndAmtThisMonth\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.711+08:00","@version":"1","message":"POST \"/S07MBIR1/getNewCountThisMonth?own=1\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.711+08:00","@version":"1","message":"POST \"/S07MBIR1/getNewCountEveryDay?own=1\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.712+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07MBIR1Controller#getNewCountAndAmtThisMonth(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.712+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M10B1Controller#getPageList(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.712+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07MBIR1Controller#getNewCountThisMonth(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.712+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07MBIR1Controller#getNewCountEveryDay(String, Integer, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.712+08:00","@version":"1","message":"Read \"application/octet-stream\" to []","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.713+08:00","@version":"1","message":"Read \"application/octet-stream\" to []","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.714+08:00","@version":"1","message":"Read \"application/octet-stream\" to []","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.715+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":1000,\"scenedata\":[{\"field\":\"Sa_Followview.IsAuto\",\"fieldtype\":0,\"math\":\"lik (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.764+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.764+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.764+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@70bef4be]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.764+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@6acaf2a6]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.765+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.767+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.781+08:00","@version":"1","message":"GET \"/S07MBIR1/countPublicLeads\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.781+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07MBIR1Controller#countPublicLeads()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.789+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.789+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@300d9b73]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.790+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.791+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.791+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@282a5558]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.802+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.837+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.837+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@5f068936]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:17.841+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-4","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:18.161+08:00","@version":"1","message":"GET \"/S07MBIR1/getCustomerType?own=1\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:18.162+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07MBIR1Controller#getCustomerType(Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:18.171+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:18.171+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@465c7573]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:18.172+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.628+08:00","@version":"1","message":"GET \"/SaFormCustom/getEntityByCode?key=S07M05B1\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.628+08:00","@version":"1","message":"POST \"/S07M05B1/getPageTh\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.629+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M05B1Controller#getPageTh(String, Integer)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.629+08:00","@version":"1","message":"Mapped to inks.sa.common.core.controller.A_SaformcustomController#getEntityByCode(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.629+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [\"{\"PageNum\":1,\"PageSize\":20,\"OrderType\":1,\"SearchType\":1,\"DateRange\":{\"StartDate\":\"2025-01-01 00:00: (truncated)...]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.639+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.639+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@7bf037b5]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.640+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.660+08:00","@version":"1","message":"GET \"/SaDgFormat/getBillEntityByCode?code=S07M05B1Th\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.661+08:00","@version":"1","message":"Mapped to inks.sa.common.core.controller.A_SadgformatController#getBillEntityByCode(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.680+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.680+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@c6f710c]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.682+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.717+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.717+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@78e8ffc]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:21.729+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-7","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.250+08:00","@version":"1","message":"GET \"/SaFormCustom/getEntityByCode?key=S07M05B1\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.251+08:00","@version":"1","message":"Mapped to inks.sa.common.core.controller.A_SaformcustomController#getEntityByCode(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.258+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.258+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@69f75dd0]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.260+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-6","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.270+08:00","@version":"1","message":"GET \"/S07M01S3/getListByShow?ud=\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.270+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M01S3Controller#getListByShow()","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.271+08:00","@version":"1","message":"GET \"/S07M05B1/getBillEntity?key=1949700483513843712\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.271+08:00","@version":"1","message":"Mapped to inks.service.sa.crm.controller.S07M05B1Controller#getBillEntity(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.283+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.283+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@120dcc22]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.284+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.284+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@7663abc1]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.286+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.287+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-5","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.349+08:00","@version":"1","message":"GET \"/SaDgFormat/getBillEntityByCode?code=S07M05B1Item\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.349+08:00","@version":"1","message":"Mapped to inks.sa.common.core.controller.A_SadgformatController#getBillEntityByCode(String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.368+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.368+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@4d13172e]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:25.371+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-8","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:27.827+08:00","@version":"1","message":"GET \"/OA/getListByModuleCode?code=S07M05B1Edit\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:27.827+08:00","@version":"1","message":"Mapped to inks.sa.common.core.config.oa.OAController#getListByModuleCode(String, String)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:28.042+08:00","@version":"1","message":"Using 'application/json', given [application/json, text/plain, */*] and supported [application/json, application/*+json, application/json, application/*+json, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8, application/xml;charset=UTF-8, text/xml;charset=UTF-8, application/*+xml;charset=UTF-8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:28.043+08:00","@version":"1","message":"Writing [inks.common.core.domain.R@6b112977]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-28T13:25:28.044+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-10657-exec-3","level":"DEBUG","level_value":10000}
