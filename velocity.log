2025-07-28 14:18:05,168 - Initializing Velocity, Calling init()...
2025-07-28 14:18:05,168 - Starting Apache Velocity v1.7 (compiled: 2010-11-19 12:14:37)
2025-07-28 14:18:05,168 - Default Properties File: org\apache\velocity\runtime\defaults\velocity.properties
2025-07-28 14:18:05,168 - Trying to use logger class org.apache.velocity.runtime.log.AvalonLogChute
2025-07-28 14:18:05,168 - Target log system for org.apache.velocity.runtime.log.AvalonLogChute is not available (java.lang.NoClassDefFoundError: org/apache/log/Priority).  Falling back to next log system...
2025-07-28 14:18:05,168 - Trying to use logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 14:18:05,168 - Using logger class org.apache.velocity.runtime.log.Log4JLogChute
2025-07-28 14:18:05,176 - ResourceLoader instantiated: org.apache.velocity.runtime.resource.loader.FileResourceLoader
2025-07-28 14:18:05,178 - Do unicode file recognition:  false
2025-07-28 14:18:05,179 - FileResourceLoader : adding path '.'
2025-07-28 14:18:05,194 - ResourceCache: initialized (class org.apache.velocity.runtime.resource.ResourceCacheImpl) with class java.util.Collections$SynchronizedMap cache map.
2025-07-28 14:18:05,196 - Loaded System Directive: org.apache.velocity.runtime.directive.Stop
2025-07-28 14:18:05,197 - Loaded System Directive: org.apache.velocity.runtime.directive.Define
2025-07-28 14:18:05,197 - Loaded System Directive: org.apache.velocity.runtime.directive.Break
2025-07-28 14:18:05,198 - Loaded System Directive: org.apache.velocity.runtime.directive.Evaluate
2025-07-28 14:18:05,198 - Loaded System Directive: org.apache.velocity.runtime.directive.Literal
2025-07-28 14:18:05,199 - Loaded System Directive: org.apache.velocity.runtime.directive.Macro
2025-07-28 14:18:05,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Parse
2025-07-28 14:18:05,200 - Loaded System Directive: org.apache.velocity.runtime.directive.Include
2025-07-28 14:18:05,201 - Loaded System Directive: org.apache.velocity.runtime.directive.Foreach
2025-07-28 14:18:05,214 - Created '20' parsers.
2025-07-28 14:18:05,218 - Velocimacro : "velocimacro.library" is not set.  Trying default library: VM_global_library.vm
2025-07-28 14:18:05,218 - Velocimacro : Default library not found.
2025-07-28 14:18:05,218 - Velocimacro : allowInline = true : VMs can be defined inline in templates
2025-07-28 14:18:05,218 - Velocimacro : allowInlineToOverride = false : VMs defined inline may NOT replace previous VM definitions
2025-07-28 14:18:05,218 - Velocimacro : allowInlineLocal = false : VMs defined inline will be global in scope if allowed.
2025-07-28 14:18:05,218 - Velocimacro : autoload off : VM system will not automatically reload global library macros
